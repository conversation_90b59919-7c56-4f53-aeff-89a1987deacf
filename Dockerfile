# Dockerfile cho Telegram Bot License Manager
FROM python:3.9-slim

# Cài đặt system dependencies
RUN apt-get update && apt-get install -y \
    dmidecode \
    util-linux \
    procps \
    && rm -rf /var/lib/apt/lists/*

# Tạo user không phải root
RUN useradd -m -u 1000 botuser

# Tạo thư mục làm việc
WORKDIR /app

# Copy requirements và cài đặt Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy source code
COPY . .

# Tạo thư mục data
RUN mkdir -p data && chown -R botuser:botuser /app

# <PERSON><PERSON><PERSON><PERSON> sang user không phải root
USER botuser

# Expose port (nếu cần)
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python3 -c "import requests; requests.get('https://api.telegram.org', timeout=5)" || exit 1

# Command mặc định
CMD ["python3", "run_telegram_bot.py"]
