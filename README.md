# Hệ thống quản lý giấy phép qua Telegram

H<PERSON> thống này cho phép quản lý giấy phép (license) thông qua bot Telegram, bao gồm các chức năng tạo, ki<PERSON><PERSON> tra, gia hạn và xóa giấy phép.

## C<PERSON>c tính năng chính

1. **<PERSON><PERSON><PERSON> thực phần cứng**: <PERSON><PERSON><PERSON> mã máy (fingerprint) duy nhất dựa trên thông tin phần cứng
2. **Quản lý qua Telegram**: <PERSON><PERSON><PERSON> thao tác đều có thể thực hiện qua bot Telegram
3. **Hỗ trợ nhiều loại license**: 
   - `usage_only`: Chỉ tính thời lượng khi sử dụng
   - `time_based`: Chỉ tính theo thời gian thực (ngày)
   - `hybrid`: <PERSON><PERSON><PERSON> cả thời lượng và thời hạn
   - `minute_based`: Tự động hết hạn sau x phút
4. **G<PERSON><PERSON><PERSON> hạn Voice ID**: <PERSON><PERSON>ể giớ<PERSON> hạn voice ID được phép sử dụng

## Cài đặt

1. Cài đặt các thư viện cần thiết:
   ```
   pip install -r requirements.txt
   ```

2. Chỉnh sửa thông tin trong file `config.py` (nếu cần):
   - GitHub Token
   - Tên repository GitHub
   - Thông tin liên hệ
   - Token Telegram

3. Chạy bot Telegram (khuyến nghị):
   ```
   python start_bot.py
   ```

   Hoặc trên Windows, double-click file `start_bot.bat`

4. Các cách chạy khác:
   ```bash
   # Chạy trực tiếp (cách cũ)
   python run_telegram_bot.py

   # Kiểm tra kết nối trước
   python check_connection.py
   ```

## Tính năng mới (v2.0)

- ✅ **Cải thiện xử lý timeout**: Bot tự động retry khi bị ngắt kết nối
- ✅ **Kiểm tra kết nối**: Kiểm tra Telegram và GitHub trước khi chạy
- ✅ **Logging chi tiết**: Ghi log vào file `telegram_bot.log`
- ✅ **Script khởi động thân thiện**: `start_bot.py` với giao diện đẹp
- ✅ **Exponential backoff**: Tăng dần thời gian chờ khi retry
- ✅ **Batch script**: `start_bot.bat` cho Windows

## Sử dụng bot Telegram

### Dành cho người dùng:
- `/start` - Xem hướng dẫn sử dụng
- `/getfingerprint` - Lấy mã máy để yêu cầu cấp license
- `/check_my_license` - Kiểm tra license của máy hiện tại

### Dành cho admin:
- `/createlic [fingerprint] [user_name] [days] [minutes] [license_model] [voice_ids]` - Tạo license mới
- `/checklic [fingerprint]` - Kiểm tra license
- `/listlic` - Liệt kê tất cả license
- `/extendlic [fingerprint] [days] [minutes]` - Gia hạn license
- `/dellic [fingerprint]` - Xóa license (cần xác nhận)
- `/add_admin [user_id]` - Thêm người quản trị mới

## Lưu ý bảo mật

- GitHub Token và Telegram Token là thông tin nhạy cảm, nên bảo vệ cẩn thận
- Chỉ cấp quyền admin cho người tin cậy
- Nên sao lưu encryption_key.bin để tránh mất dữ liệu 