# 🐧 Telegram Bot License Manager - Linux Deployment Guide

## 📋 Y<PERSON>u cầu hệ thống

### Minimum Requirements:
- **OS**: Ubuntu 18.04+, CentOS 7+, Debian 9+, hoặc bất kỳ Linux distro nào
- **Python**: 3.7+
- **RAM**: 512MB
- **Disk**: 1GB free space
- **Network**: Internet connection

### Recommended:
- **OS**: Ubuntu 20.04 LTS hoặc CentOS 8
- **Python**: 3.9+
- **RAM**: 1GB+
- **CPU**: 1 core+

## 🚀 Cài đặt nhanh

### Phương pháp 1: Script tự động
```bash
# Tải về và chạy script cài đặt
chmod +x install_linux.sh
./install_linux.sh
```

### Phương pháp 2: Cài đặt thủ công

#### Bước 1: Cài đặt Python và dependencies
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3 python3-pip python3-venv dmidecode

# CentOS/RHEL
sudo yum install python3 python3-pip dmidecode

# Arch Linux
sudo pacman -S python python-pip dmidecode
```

#### Bước 2: Clone và setup
```bash
# Tạo virtual environment
python3 -m venv venv
source venv/bin/activate

# Cài đặt dependencies
pip install -r requirements.txt
```

#### Bước 3: Cấu hình
```bash
# Copy và chỉnh sửa config
cp config.py.example config.py
nano config.py  # Điền thông tin Telegram và GitHub
```

#### Bước 4: Chạy bot
```bash
# Chạy thông thường
python3 run_telegram_bot.py

# Hoặc chạy với watchdog
python3 bot_watchdog.py
```

## 🐳 Docker Deployment

### Cài đặt Docker
```bash
# Ubuntu
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Logout và login lại
```

### Chạy với Docker Compose
```bash
# Build và chạy
docker-compose up -d

# Xem logs
docker-compose logs -f

# Dừng
docker-compose down
```

### Chạy Docker thủ công
```bash
# Build image
docker build -t telegram-bot .

# Chạy container
docker run -d \
  --name telegram-bot \
  --restart unless-stopped \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/config.py:/app/config.py:ro \
  telegram-bot
```

## ⚙️ Systemd Service (Auto-start)

### Tạo service file
```bash
sudo nano /etc/systemd/system/telegram-bot.service
```

```ini
[Unit]
Description=Telegram Bot License Manager
After=network.target

[Service]
Type=simple
User=your_username
WorkingDirectory=/path/to/bot
Environment=PATH=/path/to/bot/venv/bin
ExecStart=/path/to/bot/venv/bin/python /path/to/bot/run_telegram_bot.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### Kích hoạt service
```bash
sudo systemctl daemon-reload
sudo systemctl enable telegram-bot.service
sudo systemctl start telegram-bot.service

# Kiểm tra trạng thái
sudo systemctl status telegram-bot.service

# Xem logs
sudo journalctl -u telegram-bot.service -f
```

## 🔧 Troubleshooting

### Lỗi thường gặp

#### 1. Permission denied khi đọc hardware info
```bash
# Thêm user vào group cần thiết
sudo usermod -a -G disk $USER

# Hoặc chạy với sudo (không khuyến nghị cho production)
sudo python3 run_telegram_bot.py
```

#### 2. Module not found
```bash
# Đảm bảo virtual environment được kích hoạt
source venv/bin/activate
pip install -r requirements.txt
```

#### 3. Network connection issues
```bash
# Kiểm tra firewall
sudo ufw status
sudo ufw allow out 443  # HTTPS
sudo ufw allow out 80   # HTTP

# Kiểm tra DNS
nslookup api.telegram.org
```

#### 4. File permission issues
```bash
# Fix permissions
chmod +x *.py
chmod +x install_linux.sh
chown -R $USER:$USER .
```

## 📊 Monitoring

### Xem logs
```bash
# Bot logs
tail -f telegram_bot.log

# Watchdog logs
tail -f bot_watchdog.log

# System logs (nếu dùng systemd)
sudo journalctl -u telegram-bot.service -f
```

### Kiểm tra resource usage
```bash
# CPU và RAM
htop

# Disk space
df -h

# Network
netstat -tulpn | grep python
```

## 🔒 Security Best Practices

1. **Không chạy với root user**
2. **Sử dụng virtual environment**
3. **Backup config và encryption key**
4. **Update thường xuyên**
5. **Monitor logs để phát hiện bất thường**

## 📱 Quản lý từ xa

### SSH Tunneling (nếu cần)
```bash
# Forward port để debug
ssh -L 8080:localhost:8080 user@server
```

### Screen/Tmux session
```bash
# Chạy trong screen session
screen -S telegram-bot
python3 run_telegram_bot.py
# Ctrl+A, D để detach

# Attach lại
screen -r telegram-bot
```

## 🆘 Support

Nếu gặp vấn đề:
1. Kiểm tra logs
2. Verify config
3. Test network connectivity
4. Check file permissions

---

**Lưu ý**: Bot đã được test trên Ubuntu 20.04, CentOS 8, và Debian 11. Các distro khác có thể cần điều chỉnh nhỏ.
