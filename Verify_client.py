#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import subprocess
import uuid
import hashlib
import platform
import json
import requests
import base64
import os
import sys
import time
from datetime import datetime

# Cài đặt thư viện cryptography nếu chưa có
try:
    from cryptography.fernet import Fernet
except ImportError:
    print("Đang cài đặt thư viện cryptography...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "cryptography"])
    from cryptography.fernet import Fernet

# Định nghĩa hàm get_app_dir trước khi sử dụng
def get_app_dir():
    """L<PERSON>y thư mục <PERSON>ng dụng, hỗ trợ cả khi chạy từ Python và từ file .exe"""
    if getattr(sys, 'frozen', False):
        # Chạy từ exe
        return os.path.dirname(sys.executable)
    else:
        # Chạy từ Python
        return os.path.dirname(os.path.abspath(__file__))

# ===== CẤU HÌNH =====
# Thay thế các giá trị sau đây
GITHUB_TOKEN = "*********************************************************************************************"  # Token GitHub của bạn
GITHUB_REPO = "tuha1994/xacthuc"  # Repository của bạn
GITHUB_BRANCH = "main" 
CONTACT_INFO = "Hán Anh Tư - 0938882465 (Zalo)"  # Thông tin liên hệ

# Tạo thư mục dữ liệu trước
DATA_DIR = os.path.join(get_app_dir(), "data")
os.makedirs(DATA_DIR, exist_ok=True)

# Tạo ENCRYPTION_KEY đúng chuẩn nếu chưa có
try:
    # Thử tải key từ file
    key_file = os.path.join(get_app_dir(), "encryption_key.bin")
    
    if os.path.exists(key_file):
        with open(key_file, 'rb') as f:
            ENCRYPTION_KEY = f.read().strip()
    else:
        # Tạo key mới
        ENCRYPTION_KEY = Fernet.generate_key()
        # Lưu key để sử dụng sau này
        with open(key_file, 'wb') as f:
            f.write(ENCRYPTION_KEY)
        print(f"Đã tạo khóa mã hóa mới và lưu vào: {key_file}")
        print(f"ENCRYPTION_KEY: {ENCRYPTION_KEY.decode()}")
        print("Hãy giữ key này ở nơi an toàn!")
except Exception as e:
    print(f"Lỗi khi tạo/đọc key: {str(e)}")
    ENCRYPTION_KEY = Fernet.generate_key()  # Khởi tạo một key mới nếu có lỗi

# ===================

# ===== HÀM THU THẬP THÔNG TIN PHẦN CỨNG =====
def get_hardware_info():
    """Thu thập thông tin phần cứng để tạo định danh duy nhất"""
    info = {}
    
    # CPU ID
    try:
        if platform.system() == "Windows":
            output = subprocess.check_output("wmic cpu get ProcessorId", shell=True).decode()
            info['cpu_id'] = output.strip().split('\n')[1].strip()
        else:
            info['cpu_id'] = "unknown"
    except:
        info['cpu_id'] = "unknown"
    
    # Mainboard Serial
    try:
        if platform.system() == "Windows":
            output = subprocess.check_output("wmic baseboard get SerialNumber", shell=True).decode()
            info['mb_serial'] = output.strip().split('\n')[1].strip()
        else:
            info['mb_serial'] = "unknown"
    except:
        info['mb_serial'] = "unknown"
    
    # Disk Serial
    try:
        if platform.system() == "Windows":
            output = subprocess.check_output('wmic logicaldisk where "DeviceID=\'C:\'" get VolumeSerialNumber', shell=True).decode()
            info['disk_serial'] = output.strip().split('\n')[1].strip()
        else:
            info['disk_serial'] = "unknown"
    except:
        info['disk_serial'] = "unknown"
    
    # MAC Address
    info['mac_address'] = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                                  for elements in range(0, 48, 8)][::-1])
    
    # Thông tin OS
    info['os_name'] = platform.system()
    info['os_version'] = platform.version()
    
    return info

def generate_fingerprint(hw_info):
    """Tạo mã định danh duy nhất cho máy tính"""
    fingerprint_str = f"{hw_info['cpu_id']}:{hw_info['mb_serial']}:{hw_info['disk_serial']}:{hw_info['mac_address']}"
    return hashlib.sha256(fingerprint_str.encode('utf-8')).hexdigest()

# ===== HÀM MÃ HÓA VÀ GIẢI MÃ =====
def encrypt_data(data):
    """Mã hóa dữ liệu với khóa đã cấu hình"""
    f = Fernet(ENCRYPTION_KEY)
    json_data = json.dumps(data, ensure_ascii=False).encode('utf-8')
    return f.encrypt(json_data)

def decrypt_data(encrypted_data):
    """Giải mã dữ liệu với khóa đã cấu hình"""
    f = Fernet(ENCRYPTION_KEY)
    decrypted_data = f.decrypt(encrypted_data)
    return json.loads(decrypted_data)

# ===== HÀM XỬ LÝ GITHUB =====
def get_license_from_github(fingerprint):
    """Lấy thông tin license từ GitHub dựa trên hardware fingerprint"""
    api_url = f"https://api.github.com/repos/{GITHUB_REPO}/contents/licenses/{fingerprint}.bin"
    headers = {
        "Authorization": f"token {GITHUB_TOKEN}",
        "Accept": "application/vnd.github.v3+json"
    }
    
    try:
        # print(f"DEBUG: Đang gọi API GitHub với URL: {api_url}")
        response = requests.get(api_url, headers=headers)
        
        # print(f"DEBUG: Mã trạng thái phản hồi: {response.status_code}")
        # print(f"DEBUG: GITHUB_TOKEN: {GITHUB_TOKEN[:4]}...{GITHUB_TOKEN[-4:]}")
        # print(f"DEBUG: GITHUB_REPO: {GITHUB_REPO}")
        
        if response.status_code == 200:
            file_content = response.json()
            encrypted_data = base64.b64decode(file_content["content"])
            license_data = decrypt_data(encrypted_data)
            return True, license_data, file_content["sha"]
        else:
            error_detail = ""
            try:
                error_detail = response.json().get("message", "")
            except:
                error_detail = response.text[:100]
            
            print(f"DEBUG: Chi tiết lỗi: {error_detail}")
            return False, f"Không tìm thấy license cho fingerprint này. Lỗi: {response.status_code} - {error_detail}", None
    except Exception as e:
        print(f"DEBUG: Exception: {str(e)}")
        return False, f"Lỗi khi lấy license: {str(e)}", None

def update_license_on_github(fingerprint, license_data, file_sha=None):
    """Cập nhật thông tin license lên GitHub"""
    api_url = f"https://api.github.com/repos/{GITHUB_REPO}/contents/licenses/{fingerprint}.bin"
    headers = {
        "Authorization": f"token {GITHUB_TOKEN}",
        "Accept": "application/vnd.github.v3+json"
    }
    
    # Kiểm tra xem file đã tồn tại chưa nếu chưa có sha
    if not file_sha:
        try:
            check_response = requests.get(api_url, headers=headers)
            if check_response.status_code == 200:
                file_sha = check_response.json().get("sha")
                print(f"Debug: Đã tìm thấy license hiện có, sẽ cập nhật thay vì tạo mới.")
        except Exception as e:
            print(f"Debug: Lỗi khi kiểm tra license hiện có: {str(e)}")
    
    # Mã hóa dữ liệu
    encrypted_data = encrypt_data(license_data)
    encoded_content = base64.b64encode(encrypted_data).decode()
    
    # Chuẩn bị dữ liệu
    if file_sha:
        # Cập nhật file hiện có
        data = {
            "message": f"Cập nhật license cho {fingerprint}",
            "content": encoded_content,
            "sha": file_sha,
            "branch": GITHUB_BRANCH
        }
    else:
        # Tạo file mới
        data = {
            "message": f"Tạo license mới cho {fingerprint}",
            "content": encoded_content,
            "branch": GITHUB_BRANCH
        }
    
    try:
        response = requests.put(api_url, headers=headers, json=data)
        
        if response.status_code in [200, 201]:
            return True, "Cập nhật license thành công"
        else:
            error_detail = ""
            try:
                error_detail = response.json().get("message", "")
            except:
                error_detail = response.text
            
            return False, f"Lỗi khi cập nhật license: {response.status_code} - {error_detail}"
    except Exception as e:
        return False, f"Lỗi khi cập nhật: {str(e)}"

def delete_license(fingerprint):
    """Xóa license cho một fingerprint cụ thể"""
    api_url = f"https://api.github.com/repos/{GITHUB_REPO}/contents/licenses/{fingerprint}.bin"
    headers = {
        "Authorization": f"token {GITHUB_TOKEN}",
        "Accept": "application/vnd.github.v3+json"
    }
    
    try:
        # Kiểm tra xem file có tồn tại không
        response = requests.get(api_url, headers=headers)
        
        if response.status_code != 200:
            return False, f"Không tìm thấy license với fingerprint {fingerprint}"
        
        # Lấy sha của file
        file_sha = response.json().get("sha")
        
        # Xóa file
        delete_data = {
            "message": f"Xóa license cho {fingerprint}",
            "sha": file_sha,
            "branch": GITHUB_BRANCH
        }
        
        delete_response = requests.delete(api_url, headers=headers, json=delete_data)
        
        if delete_response.status_code == 200:
            return True, f"Đã xóa license cho {fingerprint}"
        else:
            return False, f"Lỗi khi xóa license: {delete_response.status_code} - {delete_response.text}"
            
    except Exception as e:
        return False, f"Lỗi xóa license: {str(e)}"

# Thêm hàm này vào phần HÀM XỬ LÝ GITHUB
def check_and_create_licenses_folder():
    """Kiểm tra và xác nhận thư mục licenses đã tồn tại trên GitHub"""
    api_url = f"https://api.github.com/repos/{GITHUB_REPO}/contents"
    headers = {
        "Authorization": f"token {GITHUB_TOKEN}",
        "Accept": "application/vnd.github.v3+json"
    }
    
    try:
        # Kiểm tra kết nối đến GitHub
        test_response = requests.get(f"https://api.github.com/repos/{GITHUB_REPO}", headers=headers)
        if test_response.status_code != 200:
            print(f"Không thể kết nối đến repository: {test_response.status_code}")
            if test_response.status_code == 404:
                print("Repository không tồn tại hoặc token không có quyền truy cập.")
            print(f"Response: {test_response.text}")
            return False
        
        # Lấy danh sách thư mục và file ở root
        response = requests.get(api_url, headers=headers)
        
        if response.status_code != 200:
            print(f"Không thể truy cập repository: {response.status_code}")
            print(response.text)
            return False
            
        contents = response.json()
        
        # Kiểm tra xem thư mục licenses đã tồn tại chưa
        licenses_exists = any(item.get("name") == "licenses" and item.get("type") == "dir" for item in contents)
        
        if not licenses_exists:
            print("Thư mục 'licenses' chưa tồn tại trong repository.")
            print("Vui lòng tạo thư mục này qua web interface của GitHub.")
            
            # Tạo nội dung README
            readme_content = "# Licenses Folder\nThis folder stores license files for user verification."
            encoded_content = base64.b64encode(readme_content.encode('utf-8')).decode()
            
            # Tạo thư mục bằng cách tạo file README.md trong đó
            create_response = requests.put(
                f"https://api.github.com/repos/{GITHUB_REPO}/contents/licenses/README.md",
                headers=headers,
                json={
                    "message": "Tạo thư mục licenses",
                    "content": encoded_content,
                    "branch": GITHUB_BRANCH
                }
            )
            
            if create_response.status_code in [201, 200]:
                print("Đã tạo thư mục 'licenses' thành công.")
                return True
            else:
                print(f"Không thể tạo thư mục 'licenses': {create_response.status_code}")
                print(f"Response: {create_response.text}")
                return False
            
        return True
        
    except Exception as e:
        print(f"Lỗi khi kiểm tra repository: {str(e)}")
        return False


# ===== HÀM QUẢN LÝ LICENSE =====
def create_license(fingerprint, user_name, duration_days=30, usage_limit=180, license_model="usage_only", voice_ids=None):
    """Tạo license mới cho một máy tính"""
    if not check_and_create_licenses_folder():
        return False, "Không thể tiếp tục vì thư mục licenses chưa được tạo trên GitHub"
    
    current_time = time.time()
    
    # Xử lý đặc biệt cho license kiểu minute_based
    if license_model == "minute_based":
        # Chuyển đổi thời lượng phút thành giây và đặt làm thời gian hết hạn
        expiry_time = current_time + (usage_limit * 60)
        # Đặt thời lượng là giá trị lớn để không bị hết do sử dụng
        usage_limit = 999999
        print(f"Debug: Tạo license hết hạn sau {usage_limit} phút kể từ bây giờ")
    else:
        # Xử lý các loại license khác như cũ
        if duration_days <= 0:
            if license_model == "usage_only":
                duration_days = 36500  # Khoảng 100 năm
                print("Debug: Đặt thời hạn license là 100 năm (không hết hạn theo ngày)")
            else:
                duration_days = 1
                print("Debug: Đặt thời hạn tối thiểu là 1 ngày")
        expiry_time = current_time + (duration_days * 24 * 60 * 60)
    
    print(f"Debug: Tạo license thời gian hiện tại: {datetime.fromtimestamp(current_time).strftime('%d/%m/%Y %H:%M:%S')}")
    print(f"Debug: Thời gian hết hạn: {datetime.fromtimestamp(expiry_time).strftime('%d/%m/%Y %H:%M:%S')}")
    
    # Nếu không có voice_ids, khởi tạo danh sách trống
    if voice_ids is None:
        voice_ids = []
    
    license_data = {
        "fingerprint": fingerprint,
        "user_name": user_name,
        "created_at": current_time,
        "expires_at": expiry_time,
        "usage_limit": usage_limit,
        "license_id": str(uuid.uuid4()),
        "last_check_time": current_time,
        "license_model": license_model,
        "voice_ids": voice_ids  # Thêm danh sách voice_ids vào license
    }
    
    success, message = update_license_on_github(fingerprint, license_data)
    
    if success:
        # Lưu bản sao cục bộ
        with open(os.path.join(DATA_DIR, "license_info.json"), 'w', encoding='utf-8') as f:
            json.dump(license_data, f, ensure_ascii=False, indent=4)
    
    return success, message

# hàm kiểm tra Voice ID
def check_voice_id_access(license_data, voice_id):
    """Kiểm tra xem license có quyền sử dụng voice_id hay không"""
    voice_ids = license_data.get("voice_ids", [])
    
    # Nếu danh sách trống, cho phép tất cả voice id (để tương thích ngược)
    if not voice_ids:
        print("Debug: License không có giới hạn voice ID")
        return True
    
    # Kiểm tra voice_id có trong danh sách được phép không
    if voice_id in voice_ids:
        print(f"Debug: Voice ID '{voice_id}' được phép sử dụng")
        return True
    
    print(f"Debug: Voice ID '{voice_id}' không được phép sử dụng với license này")
    print(f"Debug: Danh sách voice ID được phép: {', '.join(voice_ids)}")
    return False

def read_voice_id_from_settings():
    """Đọc Voice ID từ file settingvoice.txt"""
    settings_file = os.path.join(get_app_dir(), "settingvoice.txt")
    
    if not os.path.exists(settings_file):
        print(f"Debug: Không tìm thấy file settingvoice.txt")
        return None
    
    try:
        with open(settings_file, "r", encoding="utf-8") as f:
            settings = f.readlines()
        
        voice_id = None
        for line in settings:
            if line.startswith("giongdoc:"):
                voice_id = line.strip().split("giongdoc:")[1]
                break
        
        print(f"Debug: Đọc Voice ID từ settingvoice.txt: {voice_id}")
        return voice_id
    except Exception as e:
        print(f"Debug: Lỗi khi đọc file settingvoice.txt: {str(e)}")
        return None

def verify_license(check_voice_id=True):
    """Xác thực license của máy tính"""
    if not check_and_create_licenses_folder():
        print("Không thể tiếp tục vì thư mục licenses chưa được tạo trên GitHub")
        return False
    
    fingerprint = None
    
    # Kiểm tra file custom_fingerprint.txt (ưu tiên cao nhất)
    custom_fingerprint_file = os.path.join(get_app_dir(), "custom_fingerprint.txt")
    if os.path.exists(custom_fingerprint_file):
        try:
            with open(custom_fingerprint_file, "r") as f:
                fingerprint = f.read().strip()
            if len(fingerprint) == 64:
                print(f"Đang sử dụng mã máy từ file custom_fingerprint.txt: {fingerprint[:8]}...{fingerprint[-8:]}")
            else:
                print("File custom_fingerprint.txt không chứa mã máy hợp lệ")
                fingerprint = None
        except Exception as e:
            print(f"Lỗi khi đọc file custom_fingerprint.txt: {str(e)}")
            fingerprint = None
    
    # Sử dụng fingerprint từ hardware nếu không có custom fingerprint
    if not fingerprint:
        hw_info = get_hardware_info()
        fingerprint = generate_fingerprint(hw_info)
        print(f"Đang sử dụng mã máy từ phần cứng: {fingerprint[:8]}...{fingerprint[-8:]}")
    
    # Lưu fingerprint hiện tại vào file để tham khảo
    try:
        os.makedirs(DATA_DIR, exist_ok=True)
        with open(os.path.join(DATA_DIR, "fingerprint.txt"), 'w') as f:
            f.write(fingerprint)
        print(f"Đã lưu mã máy vào: {os.path.join(DATA_DIR, 'fingerprint.txt')}")
    except Exception as e:
        print(f"Không thể lưu mã máy: {str(e)}")
    
    # Kiểm tra license từ GitHub
    success, license_data, file_sha = get_license_from_github(fingerprint)
    
    if not success:
        print("\n===== XÁC THỰC THẤT BẠI =====")
        print(f"Lý do: {license_data}")
        print(f"Vui lòng liên hệ {CONTACT_INFO} để kích hoạt phần mềm")
        print(f"Mã máy của bạn là: {fingerprint}")
        print("Hướng dẫn kích hoạt:")
        print("1. Gửi mã máy ở trên cho người quản lý")
        print("2. Chờ thông báo license đã được tạo")
        print("3. Chạy lại phần mềm để sử dụng")
        print("=============================")
        return False
    
    # Thông tin debug
    current_time = time.time()
    print(f"Debug: Thời gian hiện tại: {datetime.fromtimestamp(current_time).strftime('%d/%m/%Y %H:%M:%S')}")
    print(f"Debug: Thời gian hết hạn: {datetime.fromtimestamp(license_data.get('expires_at', 0)).strftime('%d/%m/%Y %H:%M:%S')}")
    
    # Kiểm tra license model để áp dụng logic phù hợp
    license_model = license_data.get("license_model", "usage_only")  # Mặc định là usage_only nếu không có
    
    # Kiểm tra cả thời hạn và thời lượng
    has_expired = current_time > license_data.get("expires_at", 0)
    has_no_minutes = license_data.get("usage_limit", 0) <= 0

    # Phần code còn lại giữ nguyên
    if license_model == "hybrid":
        # Nếu hybrid, giảm thời lượng theo thời gian thực + kiểm tra cả ngày hết hạn
        if "last_check_time" in license_data:
            last_check = license_data["last_check_time"]
            elapsed_hours = (current_time - last_check) / 3600
            
            # Giảm thời lượng nhanh hơn - 1 phút cho mỗi giờ
            minutes_to_deduct = int(elapsed_hours)  # 1 phút cho mỗi 1 giờ
            
            print(f"Debug: Đã trôi qua {elapsed_hours:.2f} giờ kể từ lần kiểm tra trước")
            
            if minutes_to_deduct > 0:
                current_limit = license_data.get("usage_limit", 0)
                new_limit = max(0, current_limit - minutes_to_deduct)
                license_data["usage_limit"] = new_limit
                license_data["last_check_time"] = current_time
                
                # Lưu thay đổi
                update_success, _ = update_license_on_github(fingerprint, license_data, file_sha)
                if update_success:
                    print(f"Debug: Đã trừ {minutes_to_deduct} phút do thời gian không hoạt động ({current_limit} -> {new_limit})")
                else:
                    print("Debug: Không thể cập nhật license trên GitHub")
            else:
                print("Debug: Chưa đủ thời gian để trừ phút (cần ít nhất 1 giờ)")
        else:
            print("Debug: Đây là lần đầu kiểm tra, thiết lập thời gian bắt đầu")
            license_data["last_check_time"] = current_time
            update_license_on_github(fingerprint, license_data, file_sha)
        
        if has_expired and has_no_minutes:
            print("License đã hết hạn và thời lượng đã hết!")
            print(f"Vui lòng liên hệ {CONTACT_INFO} để gia hạn")
            return False
        elif has_expired:
            print("Cảnh báo: License đã hết hạn nhưng vẫn còn thời lượng để sử dụng.")
            print(f"Còn lại: {license_data.get('usage_limit', 0)} phút")
            print(f"Vui lòng sớm liên hệ {CONTACT_INFO} để gia hạn")
            # Vẫn cho phép sử dụng nếu còn thời lượng
        elif has_no_minutes:
            print("Thời lượng sử dụng đã hết!")
            print(f"Vui lòng liên hệ {CONTACT_INFO} để nạp thêm")
            return False
    elif license_model == "time_based":
        # Chỉ quan tâm đến thời hạn
        if has_expired:
            print("License đã hết hạn!")
            print(f"Vui lòng liên hệ {CONTACT_INFO} để gia hạn")
            return False
    elif license_model == "minute_based":
        # Kiểu minute_based chỉ kiểm tra thời gian hết hạn tính bằng phút
        if has_expired:
            # Tính thời gian đã trôi qua từ khi tạo license đến hiện tại
            created_at = license_data.get("created_at", 0)
            elapsed_minutes = int((current_time - created_at) / 60)
            print(f"License đã hết hạn! Đã trôi qua {elapsed_minutes} phút kể từ khi tạo.")
            print(f"Vui lòng liên hệ {CONTACT_INFO} để gia hạn")
            return False
    else:  # usage_only
        # Chỉ quan tâm đến thời lượng
        if has_no_minutes:
            print("Thời lượng sử dụng đã hết!")
            print(f"Vui lòng liên hệ {CONTACT_INFO} để nạp thêm")
            return False
    
    # Thêm kiểm tra Voice ID
    if check_voice_id:
        voice_id = read_voice_id_from_settings()
        if voice_id and not check_voice_id_access(license_data, voice_id):
            print("\n===== XÁC THỰC THẤT BẠI =====")
            print(f"Lý do: Voice ID '{voice_id}' không được phép sử dụng với license này")
            print(f"Vui lòng liên hệ {CONTACT_INFO} để được cấp quyền sử dụng Voice ID này")
            print("=============================")
            return False

    print("\n===== THÔNG TIN LICENSE =====")
    print(f"Người dùng: {license_data.get('user_name', 'Không xác định')}")

    expiry_date = datetime.fromtimestamp(license_data.get('expires_at', 0))
    days_left = (expiry_date - datetime.now()).days

    if license_model == "minute_based":
        seconds_left = max(0, license_data.get("expires_at", 0) - current_time)
        minutes_left = int(seconds_left / 60)
        print(f"Thời gian hết hạn: {expiry_date.strftime('%d/%m/%Y %H:%M:%S')}")
        print(f"Còn lại: {minutes_left} phút")
    else:
        print(f"Ngày hết hạn: {expiry_date.strftime('%d/%m/%Y')} (còn {days_left} ngày)")
        print(f"Thời lượng còn lại: {license_data.get('usage_limit', 0)} phút")
    print("=============================\n")
    
    return True

def verify_specific_license(fingerprint):
    """Xác thực license cho một fingerprint cụ thể"""
    if not check_and_create_licenses_folder():
        print("Không thể tiếp tục vì thư mục licenses chưa được tạo trên GitHub")
        return False
    
    print(f"DEBUG: Đang kiểm tra license cho fingerprint: {fingerprint}")
    
    # Kiểm tra license từ GitHub
    success, license_data, file_sha = get_license_from_github(fingerprint)
    
    if not success:
        print(f"Xác thực thất bại: {license_data}")
        return False
    
    # Thông tin debug
    current_time = time.time()
    print(f"DEBUG: Thời gian hiện tại: {datetime.fromtimestamp(current_time).strftime('%d/%m/%Y %H:%M:%S')}")
    print(f"DEBUG: Thời gian hết hạn: {datetime.fromtimestamp(license_data.get('expires_at', 0)).strftime('%d/%m/%Y %H:%M:%S')}")
    print(f"DEBUG: License data: {json.dumps(license_data, indent=2, ensure_ascii=False)}")
    
    # Kiểm tra license model để áp dụng logic phù hợp
    license_model = license_data.get("license_model", "usage_only")
    
    print("\n===== THÔNG TIN LICENSE =====")
    print(f"Fingerprint: {fingerprint[:8]}...{fingerprint[-8:]}")
    print(f"Người dùng: {license_data.get('user_name', 'Không xác định')}")
    print(f"Loại license: {license_model}")
    
    expiry_date = datetime.fromtimestamp(license_data.get("expires_at", 0))
    days_left = (expiry_date - datetime.now()).days
    
    if license_model == "minute_based":
        # Đối với license minute_based, tính toán số phút còn lại đến khi hết hạn
        seconds_left = max(0, license_data.get("expires_at", 0) - current_time)
        minutes_left = int(seconds_left / 60)
        print(f"Thời gian hết hạn: {expiry_date.strftime('%d/%m/%Y %H:%M:%S')}")
        print(f"Còn lại: {minutes_left} phút")
    else:
        # Các license khác hiển thị ngày hết hạn và thời lượng
        print(f"Ngày hết hạn: {expiry_date.strftime('%d/%m/%Y')} (còn {days_left} ngày)")
        print(f"Thời lượng còn lại: {license_data.get('usage_limit', 0)} phút")
    
    # Thêm hiển thị thông tin Voice ID
    voice_ids = license_data.get("voice_ids", [])
    if voice_ids:
        print(f"Voice IDs được phép: {', '.join(voice_ids)}")
    else:
        print("Voice IDs: Không giới hạn (tất cả đều được phép)")
    
    print("=============================\n")
    
    return True

def update_usage(minutes_used):
    """Cập nhật thời lượng sử dụng"""
    hw_info = get_hardware_info()
    fingerprint = generate_fingerprint(hw_info)
    
    # Lấy license hiện tại
    success, license_data, file_sha = get_license_from_github(fingerprint)
    
    if not success:
        print(f"Không thể cập nhật thời lượng: {license_data}")
        return False
    
    # Giảm thời lượng
    current_limit = license_data.get("usage_limit", 0)
    new_limit = max(0, current_limit - minutes_used)
    license_data["usage_limit"] = new_limit
    
    # Cập nhật lên GitHub
    update_success, message = update_license_on_github(fingerprint, license_data, file_sha)
    
    if update_success:
        print(f"Đã cập nhật thời lượng, còn lại {new_limit} phút")
        return True
    else:
        print(f"Lỗi khi cập nhật thời lượng: {message}")
        return False

def list_all_licenses():
    """Liệt kê tất cả license trong hệ thống"""
    api_url = f"https://api.github.com/repos/{GITHUB_REPO}/contents/licenses"
    headers = {
        "Authorization": f"token {GITHUB_TOKEN}",
        "Accept": "application/vnd.github.v3+json"
    }
    
    try:
        response = requests.get(api_url, headers=headers)
        
        if response.status_code != 200:
            print(f"Lỗi khi truy cập thư mục licenses: {response.status_code}")
            return
        
        files = response.json()
        license_files = [f for f in files if f.get("name", "").endswith('.bin')]
        
        if not license_files:
            print("Chưa có license nào trong hệ thống")
            return
        
        print(f"\nĐã tìm thấy {len(license_files)} license:")
        current_time = time.time()  # Lấy thời gian hiện tại
        
        for i, file in enumerate(license_files, 1):
            file_name = file.get("name", "")
            fingerprint = file_name.replace('.bin', '')
            
            # Lấy nội dung file
            file_url = file.get("download_url")
            if file_url:
                try:
                    file_response = requests.get(file_url, headers=headers)
                    if file_response.status_code == 200:
                        # Lấy nội dung đã được mã hóa base64 từ GitHub API
                        content_response = requests.get(f"https://api.github.com/repos/{GITHUB_REPO}/contents/licenses/{file_name}", headers=headers)
                        if content_response.status_code == 200:
                            content_data = content_response.json()
                            content = content_data.get("content", "")
                            # GitHub thêm \n vào chuỗi base64, cần loại bỏ
                            content = content.replace("\n", "")
                            
                            # Giải mã base64 và decrypt dữ liệu
                            encrypted_data = base64.b64decode(content)
                            license_data = decrypt_data(encrypted_data)
                            
                            # Lấy loại license
                            license_model = license_data.get("license_model", "usage_only")
                            
                            # print(f"\n{i}. Fingerprint: {fingerprint[:8]}...{fingerprint[-8:]}")
                            print(f"\n{i}. Fingerprint: {fingerprint}")
                            print(f"   Người dùng: {license_data.get('user_name', 'Không xác định')}")
                            print(f"   Loại license: {license_model}")
                            
                            if 'expires_at' in license_data:
                                expiry_date = datetime.fromtimestamp(license_data['expires_at'])
                                days_left = (expiry_date - datetime.now()).days
                                print(f"   Hết hạn: {expiry_date.strftime('%d/%m/%Y')} (còn {days_left} ngày)")
                            
                            # Xử lý đặc biệt cho license loại minute_based
                            if license_model == "minute_based":
                                # Tính số phút còn lại
                                seconds_left = max(0, license_data.get("expires_at", 0) - current_time)
                                minutes_left = int(seconds_left / 60)
                                print(f"   Thời gian còn lại: {minutes_left} phút")
                                
                                # Hiển thị trạng thái
                                if seconds_left <= 0:
                                    print(f"   Trạng thái: ĐÃ HẾT HẠN")
                                else:
                                    print(f"   Trạng thái: Còn hạn")
                            else:
                                # Hiển thị thời lượng thông thường
                                print(f"   Thời lượng: {license_data.get('usage_limit', 0)} phút")
                                
                                # Hiển thị trạng thái
                                has_expired = current_time > license_data.get("expires_at", 0)
                                has_no_minutes = license_data.get("usage_limit", 0) <= 0
                                
                                if has_expired and has_no_minutes:
                                    print(f"   Trạng thái: HẾT HẠN và HẾT THỜI LƯỢNG")
                                elif has_expired:
                                    print(f"   Trạng thái: HẾT HẠN (còn thời lượng)")
                                elif has_no_minutes:
                                    print(f"   Trạng thái: HẾT THỜI LƯỢNG (còn hạn)")
                                else:
                                    print(f"   Trạng thái: Còn hạn")
                            
                            # Thêm hiển thị thông tin Voice ID
                            voice_ids = license_data.get("voice_ids", [])
                            if voice_ids:
                                print(f"   Voice IDs: {', '.join(voice_ids)}")
                            else:
                                print("   Voice IDs: Không giới hạn")
                        else:
                            print(f"\n{i}. {fingerprint[:8]}...{fingerprint[-8:]} (Không đọc được nội dung)")
                    else:
                        print(f"\n{i}. {fingerprint[:8]}...{fingerprint[-8:]} (Không đọc được)")
                except Exception as e:
                    print(f"\n{i}. {fingerprint[:8]}...{fingerprint[-8:]} (Lỗi: {str(e)})")
            else:
                print(f"\n{i}. {fingerprint[:8]}...{fingerprint[-8:]} (Không có URL)")
                
    except Exception as e:
        print(f"Lỗi khi liệt kê licenses: {str(e)}")

def extend_license(fingerprint, additional_days=0, additional_minutes=0):
    """Gia hạn license cho một máy tính"""
    success, license_data, file_sha = get_license_from_github(fingerprint)
    
    if not success:
        return False, f"Không tìm thấy license cho {fingerprint}"
    
    # Lấy thông tin license
    license_model = license_data.get("license_model", "usage_only")
    current_time = time.time()
    
    # Xử lý tùy theo loại license
    if license_model == "minute_based":
        # Đối với minute_based, cộng thêm thời gian vào thời điểm hết hạn
        if additional_minutes > 0:
            current_expiry = license_data.get("expires_at", current_time)
            # Nếu đã hết hạn, tính từ thời điểm hiện tại
            if current_expiry < current_time:
                license_data["expires_at"] = current_time + (additional_minutes * 60)
            else:
                # Nếu chưa hết hạn, cộng thêm thời gian
                license_data["expires_at"] = current_expiry + (additional_minutes * 60)
            
            print(f"Debug: Đã gia hạn thêm {additional_minutes} phút cho license minute_based")
            print(f"Debug: Thời gian hết hạn mới: {datetime.fromtimestamp(license_data['expires_at']).strftime('%d/%m/%Y %H:%M:%S')}")
    else:
        # Gia hạn thời hạn ngày
        if additional_days > 0:
            current_expiry = license_data.get("expires_at", current_time)
            # Nếu đã hết hạn, tính từ thời điểm hiện tại
            if current_expiry < current_time:
                license_data["expires_at"] = current_time + (additional_days * 24 * 60 * 60)
            else:
                # Nếu chưa hết hạn, cộng thêm thời gian
                license_data["expires_at"] = current_expiry + (additional_days * 24 * 60 * 60)
        
        # Thêm thời lượng sử dụng (phút)
        if additional_minutes > 0:
            current_limit = license_data.get("usage_limit", 0)
            new_limit = current_limit + additional_minutes
            license_data["usage_limit"] = new_limit
            print(f"Debug: Đã thêm {additional_minutes} phút thời lượng, tổng mới: {new_limit} phút")
    
    # Cập nhật thời gian kiểm tra
    license_data["last_check_time"] = current_time
    
    # Lưu thay đổi
    return update_license_on_github(fingerprint, license_data, file_sha)

# ===== HÀM TIỆN ÍCH =====
def generate_new_key():
    """Tạo khóa mã hóa mới"""
    key = Fernet.generate_key()
    print(f"Khóa mã hóa mới: {key.decode()}")
    print("Hãy cập nhật giá trị ENCRYPTION_KEY trong mã nguồn với khóa này")
    return key

# ===== PHẦN CHÍNH CỦA CHƯƠNG TRÌNH =====
def license_manager_mode():
    """Chế độ quản lý license"""
    
    while True:  # Thay đổi thành vòng lặp thay vì đệ quy
        print("\n===== QUẢN LÝ LICENSE =====")
        print("1. Tạo license mới cho máy này")
        print("1b. Tạo license cho máy khác (nhập fingerprint)")
        print("2. Kiểm tra license hiện tại")
        print("2b. Kiểm tra license cho máy khác")
        print("3. Tạo khóa mã hóa mới")
        print("4. Hiển thị thông tin phần cứng")
        print("5. Liệt kê tất cả license")
        print("6. Xóa license")
        print("7. Gia hạn license")
        print("8. Quản lý Voice IDs")
        print("9. Debug: Hiển thị cấu hình GitHub")
        print("10. Debug: Kiểm tra thư mục licenses trên GitHub")
        print("0. Thoát")
        
        choice = input("Chọn chức năng: ")
        
        if choice == "0":
            print("Đã thoát chế độ quản lý license.")
            break  # Thoát khỏi vòng lặp khi chọn 0
        
        # Các chức năng khác giữ nguyên
        elif choice == "1" or choice == "1b":
            if choice == "1":
                hw_info = get_hardware_info()
                fingerprint = generate_fingerprint(hw_info)
                print(f"Fingerprint: {fingerprint}")
            else:
                fingerprint = input("Nhập fingerprint của máy cần tạo license: ").strip()
                if len(fingerprint) != 64:
                    print("Fingerprint không hợp lệ! Phải có độ dài 64 ký tự.")
                    input("\nNhấn Enter để tiếp tục...")
                    continue
            
            user_name = input("Nhập tên người dùng: ")
            try:
                duration = int(input("Nhập thời hạn (ngày, mặc định 30, 0 = không hết hạn nếu là license loại usage_only): ") or "30")
                usage_limit = int(input("Nhập thời lượng (phút, mặc định 180): ") or "180")
            except ValueError:
                print("Giá trị không hợp lệ! Sử dụng giá trị mặc định.")
                duration = 30
                usage_limit = 180
            
            # Chọn kiểu license
            print("\nChọn kiểu license:")
            print("1. Chỉ tính thời lượng khi sử dụng (usage_only)")
            print("2. Chỉ tính theo thời gian thực - ngày (time_based)")
            print("3. Tính cả hai (hybrid)")
            print("4. Tính theo phút - tự động hết hạn sau x phút (minute_based)")
            
            model_choice = input("Lựa chọn của bạn (mặc định: 1): ") or "1"
            
            if model_choice == "1":
                license_model = "usage_only"
            elif model_choice == "2":
                license_model = "time_based"
            elif model_choice == "3":
                license_model = "hybrid"
            elif model_choice == "4":
                license_model = "minute_based"
                # Thêm thông báo để người dùng hiểu
                print("Với kiểu minute_based, license sẽ tự động hết hạn sau số phút bạn nhập, không phụ thuộc việc sử dụng")
            else:
                print("Lựa chọn không hợp lệ! Sử dụng mặc định (usage_only).")
                license_model = "usage_only"
            
            # Thêm phần nhập Voice ID
            print("\nNhập các Voice ID được phép sử dụng (phân cách bằng dấu phẩy, ví dụ: GIRL01,BOY01)")
            print("Để trống nếu không giới hạn Voice ID")
            voice_ids_input = input("Voice IDs: ").strip()
            
            if voice_ids_input:
                voice_ids = [vid.strip() for vid in voice_ids_input.split(",")]
            else:
                voice_ids = []
            
            success, message = create_license(fingerprint, user_name, duration, usage_limit, license_model, voice_ids)
            print(message)
        
        elif choice == "2":
            verify_license()
        
        elif choice == "2b":
            fingerprint = input("Nhập fingerprint của máy cần kiểm tra: ").strip()
            if len(fingerprint) != 64:
                print("Fingerprint không hợp lệ! Phải có độ dài 64 ký tự.")
            else:
                verify_specific_license(fingerprint)
        
        elif choice == "3":
            generate_new_key()
        
        elif choice == "4":
            hw_info = get_hardware_info()
            print("\n===== THÔNG TIN PHẦN CỨNG =====")
            for key, value in hw_info.items():
                print(f"{key}: {value}")
            print(f"Fingerprint: {generate_fingerprint(hw_info)}")
        
        elif choice == "5":
            list_all_licenses()
        
        elif choice == "6":
            fingerprint = input("Nhập fingerprint cần xóa: ").strip()
            if len(fingerprint) != 64:
                print("Fingerprint không hợp lệ! Phải có độ dài 64 ký tự.")
            else:
                confirm = input(f"Bạn có chắc chắn muốn xóa license cho {fingerprint[:8]}...{fingerprint[-8:]}? (y/n): ")
                if confirm.lower() == 'y':
                    success, message = delete_license(fingerprint)
                    print(message)
        
        elif choice == "7":
            fingerprint = input("Nhập fingerprint cần gia hạn: ").strip()
            if len(fingerprint) != 64:
                print("Fingerprint không hợp lệ! Phải có độ dài 64 ký tự.")
            else:
                # Kiểm tra loại license hiện tại
                success, license_data, file_sha = get_license_from_github(fingerprint)
                if not success:
                    print(f"Không tìm thấy license cho fingerprint này: {license_data}")
                else:
                    license_model = license_data.get("license_model", "usage_only")
                    print(f"Loại license hiện tại: {license_model}")
                    
                    try:
                        if license_model == "minute_based":
                            print("Đây là license loại minute_based, thời gian sẽ được thêm vào thời điểm hết hạn")
                            add_mins = int(input("Thêm bao nhiêu phút? (ví dụ: 5): ") or "0")
                            add_days = 0  # Không thêm ngày cho license minute_based
                        else:
                            add_days = int(input("Thêm bao nhiêu ngày? (0 để không thay đổi): ") or "0")
                            add_mins = int(input("Thêm bao nhiêu phút thời lượng? (0 để không thay đổi): ") or "0")
                        
                        if add_days == 0 and add_mins == 0:
                            print("Bạn phải nhập ít nhất một giá trị > 0")
                        else:
                            success, message = extend_license(fingerprint, add_days, add_mins)
                            print(message)
                            if success:
                                print("Đã gia hạn license thành công!")
                                verify_specific_license(fingerprint)  # Hiển thị thông tin license sau khi gia hạn
                            
                    except ValueError:
                        print("Giá trị không hợp lệ!")
        
        elif choice == "8":
            fingerprint = input("Nhập fingerprint cần quản lý Voice IDs: ").strip()
            if len(fingerprint) != 64:
                print("Fingerprint không hợp lệ! Phải có độ dài 64 ký tự.")
            else:
                success, license_data, file_sha = get_license_from_github(fingerprint)
                if not success:
                    print(f"Không tìm thấy license cho fingerprint này: {license_data}")
                else:
                    # Hiển thị danh sách Voice ID hiện tại
                    voice_ids = license_data.get("voice_ids", [])
                    print("\n===== VOICE IDs HIỆN TẠI =====")
                    if voice_ids:
                        for i, vid in enumerate(voice_ids, 1):
                            print(f"{i}. {vid}")
                    else:
                        print("Không giới hạn (tất cả Voice IDs đều được phép)")
                    
                    # Menu quản lý Voice ID
                    print("\n1. Thêm Voice ID")
                    print("2. Xóa Voice ID")
                    print("3. Đặt lại toàn bộ Voice ID")
                    print("0. Quay lại")
                    
                    sub_choice = input("Chọn chức năng: ")
                    
                    if sub_choice == "1":
                        new_voice_id = input("Nhập Voice ID mới: ").strip()
                        if new_voice_id:
                            if "voice_ids" not in license_data:
                                license_data["voice_ids"] = []
                            if new_voice_id not in license_data["voice_ids"]:
                                license_data["voice_ids"].append(new_voice_id)
                                success, message = update_license_on_github(fingerprint, license_data, file_sha)
                                if success:
                                    print(f"Đã thêm Voice ID {new_voice_id} thành công!")
                                else:
                                    print(f"Lỗi khi cập nhật: {message}")
                            else:
                                print(f"Voice ID {new_voice_id} đã tồn tại trong license!")
                    
                    elif sub_choice == "2":
                        if not voice_ids:
                            print("Không có Voice ID nào để xóa!")
                        else:
                            remove_id = input("Nhập Voice ID cần xóa: ").strip()
                            if remove_id in license_data["voice_ids"]:
                                license_data["voice_ids"].remove(remove_id)
                                success, message = update_license_on_github(fingerprint, license_data, file_sha)
                                if success:
                                    print(f"Đã xóa Voice ID {remove_id} thành công!")
                                else:
                                    print(f"Lỗi khi cập nhật: {message}")
                            else:
                                print(f"Không tìm thấy Voice ID {remove_id} trong license!")
                    
                    elif sub_choice == "3":
                        new_voice_ids = input("Nhập danh sách Voice ID mới (phân cách bằng dấu phẩy, để trống cho phép tất cả): ").strip()
                        if new_voice_ids:
                            license_data["voice_ids"] = [vid.strip() for vid in new_voice_ids.split(",")]
                        else:
                            license_data["voice_ids"] = []
                        
                        success, message = update_license_on_github(fingerprint, license_data, file_sha)
                        if success:
                            print("Đã cập nhật danh sách Voice ID thành công!")
                        else:
                            print(f"Lỗi khi cập nhật: {message}")
        
        elif choice == "9":
            print("\n===== DEBUG: CẤU HÌNH GITHUB =====")
            print(f"GITHUB_TOKEN: {GITHUB_TOKEN[:4]}...{GITHUB_TOKEN[-4:]} (Độ dài: {len(GITHUB_TOKEN)})")
            print(f"GITHUB_REPO: {GITHUB_REPO}")
            print(f"GITHUB_BRANCH: {GITHUB_BRANCH}")
            
            # Kiểm tra kết nối đến GitHub
            print("\nĐang kiểm tra kết nối đến GitHub...")
            api_url = f"https://api.github.com/repos/{GITHUB_REPO}"
            headers = {
                "Authorization": f"token {GITHUB_TOKEN}",
                "Accept": "application/vnd.github.v3+json"
            }
            
            try:
                response = requests.get(api_url, headers=headers)
                
                if response.status_code == 200:
                    repo_data = response.json()
                    print(f"✓ Kết nối thành công!")
                    print(f"Repository: {repo_data.get('full_name')}")
                    print(f"Private: {repo_data.get('private')}")
                    print(f"Default branch: {repo_data.get('default_branch')}")
                else:
                    print(f"✗ Không thể kết nối đến repository!")
                    print(f"Status code: {response.status_code}")
                    try:
                        error_detail = response.json().get("message", "")
                        print(f"Error: {error_detail}")
                    except:
                        print(f"Response: {response.text[:100]}")
            except Exception as e:
                print(f"✗ Lỗi khi kết nối: {str(e)}")
            
            print("\n===== KẾT THÚC DEBUG =====")
        
        elif choice == "10":
            print("\n===== DEBUG: KIỂM TRA THƯ MỤC LICENSES =====")
            api_url = f"https://api.github.com/repos/{GITHUB_REPO}/contents/licenses"
            headers = {
                "Authorization": f"token {GITHUB_TOKEN}",
                "Accept": "application/vnd.github.v3+json"
            }
            
            try:
                response = requests.get(api_url, headers=headers)
                
                if response.status_code == 200:
                    files = response.json()
                    print(f"✓ Đọc thư mục licenses thành công!")
                    print(f"Số lượng files: {len(files)}")
                    
                    print("\nDanh sách tất cả files trong thư mục licenses:")
                    for i, file in enumerate(files, 1):
                        name = file.get("name", "")
                        size = file.get("size", 0)
                        file_type = file.get("type", "")
                        print(f"{i}. {name} ({file_type}, {size} bytes)")
                    
                    # Kiểm tra xem fingerprint đang tìm có trong danh sách không
                    finger_to_check = input("\nNhập fingerprint để kiểm tra có trong danh sách không (Enter để bỏ qua): ").strip()
                    if finger_to_check:
                        found = False
                        for file in files:
                            if file.get("name") == f"{finger_to_check}.bin":
                                found = True
                                print(f"\n✓ Tìm thấy file license cho fingerprint {finger_to_check}!")
                                
                                # Thử đọc nội dung file này
                                file_url = file.get("url")
                                file_response = requests.get(file_url, headers=headers)
                                if file_response.status_code == 200:
                                    file_data = file_response.json()
                                    print(f"SHA: {file_data.get('sha')}")
                                    print(f"Size: {file_data.get('size')} bytes")
                                    print(f"Encoding: {file_data.get('encoding')}")
                                    break
                        
                        if not found:
                            print(f"\n✗ Không tìm thấy file license cho fingerprint {finger_to_check}!")
                else:
                    print(f"✗ Không thể đọc thư mục licenses!")
                    print(f"Status code: {response.status_code}")
                    try:
                        error_detail = response.json().get("message", "")
                        print(f"Error: {error_detail}")
                    except:
                        print(f"Response: {response.text[:100]}")
            except Exception as e:
                print(f"✗ Lỗi khi kiểm tra: {str(e)}")
            
            print("\n===== KẾT THÚC DEBUG =====")
        
        input("\nNhấn Enter để tiếp tục...")

def user_mode():
    """Chế độ người dùng - chạy ứng dụng chính"""
    # Xác thực license trước khi chạy
    if not verify_license():
        print("Xác thực thất bại, chương trình sẽ thoát.")
        input("Nhấn Enter để thoát...")
        sys.exit(1)
    
    # Ghi lại thời gian bắt đầu
    start_time = time.time()
    
    try:
        # === CODE CHÍNH CỦA ỨNG DỤNG CỦA BẠN ĐẶT Ở ĐÂY ===
        print("\nXin chào thế giới! Đây là ứng dụng đã được bảo vệ bằng license.")
        print("Các chức năng chính của ứng dụng sẽ được đặt ở đây.")
        
        # Chỉ để mô phỏng ứng dụng đang chạy
        input("\nNhấn Enter để kết thúc chương trình...")
        # === KẾT THÚC CODE CHÍNH ===
    
    finally:
        # Tính thời gian sử dụng và cập nhật
        end_time = time.time()
        minutes_used = int((end_time - start_time) / 60) + 1  # Làm tròn lên 1 phút
        
        if minutes_used > 0:
            print(f"Bạn đã sử dụng ứng dụng trong {minutes_used} phút.")
            update_usage(minutes_used)

if __name__ == "__main__":
    # Kiểm tra chế độ chạy
    if len(sys.argv) > 1 and sys.argv[1] == "--admin":
        # Chế độ quản lý license
        license_manager_mode()
    else:
        # Chế độ người dùng bình thường
        user_mode()