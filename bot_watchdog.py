#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Bot Watchdog - Giám sát và tự động restart Telegram Bot
Chạy script này để đảm bảo bot luôn hoạt động ổn định
"""

import os
import sys
import time
import subprocess
import logging
import signal
import psutil
from datetime import datetime

# C<PERSON><PERSON> hình logging cho watchdog
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - WATCHDOG - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot_watchdog.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BotWatchdog:
    def __init__(self):
        self.bot_process = None
        self.restart_count = 0
        self.last_restart_time = None
        self.max_restarts_per_hour = 10
        self.running = True
        
        # Setup signal handlers để graceful shutdown
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
    def signal_handler(self, signum, frame):
        """Xử lý tín hiệu dừng"""
        logger.info(f"Nhận tín hiệu {signum}, đang dừng watchdog...")
        self.running = False
        self.stop_bot()
        
    def is_bot_running(self):
        """Kiểm tra xem bot có đang chạy không"""
        if self.bot_process is None:
            return False
            
        # Kiểm tra process có còn sống không
        if self.bot_process.poll() is None:
            return True
        else:
            logger.warning(f"Bot process đã dừng với exit code: {self.bot_process.returncode}")
            return False
    
    def start_bot(self):
        """Khởi động bot"""
        try:
            logger.info("Đang khởi động Telegram Bot...")
            
            # Khởi động bot process
            self.bot_process = subprocess.Popen([
                sys.executable, "run_telegram_bot.py"
            ], 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE,
            universal_newlines=True,
            bufsize=1
            )
            
            logger.info(f"Bot đã được khởi động với PID: {self.bot_process.pid}")
            
            # Cập nhật thống kê restart
            current_time = datetime.now()
            if self.last_restart_time and (current_time - self.last_restart_time).seconds < 3600:
                self.restart_count += 1
            else:
                self.restart_count = 1
            
            self.last_restart_time = current_time
            
            return True
            
        except Exception as e:
            logger.error(f"Lỗi khi khởi động bot: {str(e)}")
            return False
    
    def stop_bot(self):
        """Dừng bot một cách graceful"""
        if self.bot_process:
            try:
                logger.info("Đang dừng bot...")
                
                # Thử terminate trước
                self.bot_process.terminate()
                
                # Đợi 10 giây để bot tự dừng
                try:
                    self.bot_process.wait(timeout=10)
                    logger.info("Bot đã dừng gracefully")
                except subprocess.TimeoutExpired:
                    # Nếu không dừng được, kill process
                    logger.warning("Bot không dừng gracefully, đang force kill...")
                    self.bot_process.kill()
                    self.bot_process.wait()
                    logger.info("Bot đã được force killed")
                    
            except Exception as e:
                logger.error(f"Lỗi khi dừng bot: {str(e)}")
            finally:
                self.bot_process = None
    
    def restart_bot(self):
        """Restart bot"""
        logger.info("Đang restart bot...")
        self.stop_bot()
        time.sleep(5)  # Đợi 5 giây trước khi start lại
        return self.start_bot()
    
    def check_system_resources(self):
        """Kiểm tra tài nguyên hệ thống"""
        try:
            # Kiểm tra RAM
            memory = psutil.virtual_memory()
            if memory.percent > 90:
                logger.warning(f"RAM usage cao: {memory.percent}%")
                return False
            
            # Kiểm tra disk space
            disk = psutil.disk_usage('/')
            if disk.percent > 95:
                logger.warning(f"Disk usage cao: {disk.percent}%")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"Lỗi khi kiểm tra system resources: {str(e)}")
            return True  # Không ngăn bot chạy nếu không check được
    
    def check_bot_health(self):
        """Kiểm tra sức khỏe của bot"""
        if not self.is_bot_running():
            return False
            
        # Kiểm tra xem process có stuck không (optional)
        # Có thể thêm logic kiểm tra log file, API response, etc.
        
        return True
    
    def run(self):
        """Chạy watchdog chính"""
        logger.info("=== Bot Watchdog bắt đầu hoạt động ===")
        logger.info("Nhấn Ctrl+C để dừng watchdog")
        
        # Khởi động bot lần đầu
        if not self.start_bot():
            logger.error("Không thể khởi động bot lần đầu!")
            return
        
        while self.running:
            try:
                # Kiểm tra bot có đang chạy không
                if not self.check_bot_health():
                    logger.warning("Bot không hoạt động, cần restart...")
                    
                    # Kiểm tra xem có restart quá nhiều không
                    if self.restart_count >= self.max_restarts_per_hour:
                        logger.error(f"Đã restart quá nhiều lần ({self.restart_count}) trong 1 giờ. Dừng watchdog để tránh loop.")
                        break
                    
                    # Restart bot
                    if not self.restart_bot():
                        logger.error("Không thể restart bot!")
                        time.sleep(30)  # Đợi lâu hơn nếu restart fail
                        continue
                
                # Kiểm tra system resources
                if not self.check_system_resources():
                    logger.warning("System resources thấp, nhưng tiếp tục giám sát...")
                
                # Đợi 30 giây trước khi check tiếp
                time.sleep(30)
                
            except KeyboardInterrupt:
                logger.info("Nhận Ctrl+C, đang dừng watchdog...")
                break
            except Exception as e:
                logger.error(f"Lỗi trong watchdog loop: {str(e)}")
                time.sleep(10)
        
        # Cleanup
        self.stop_bot()
        logger.info("=== Bot Watchdog đã dừng ===")

def main():
    """Hàm main"""
    # Kiểm tra đã có watchdog khác chạy chưa
    current_pid = os.getpid()
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = proc.info.get('cmdline')
            if (proc.info['pid'] != current_pid and 
                cmdline and 
                isinstance(cmdline, list) and 
                'bot_watchdog.py' in ' '.join(cmdline)):
                print(f"Watchdog khác đã đang chạy (PID: {proc.info['pid']})")
                print("Dừng watchdog cũ trước khi chạy cái mới.")
                return
        except (psutil.NoSuchProcess, psutil.AccessDenied, TypeError, AttributeError):
            # Ignore processes we can't access or have invalid cmdline
            pass
    
    # Khởi tạo và chạy watchdog
    watchdog = BotWatchdog()
    watchdog.run()

if __name__ == "__main__":
    main() 