version: '3.8'

services:
  telegram-bot:
    build: .
    container_name: telegram-bot-license
    restart: unless-stopped
    
    # Mount volumes để persist data
    volumes:
      - ./data:/app/data
      - ./config.py:/app/config.py:ro
      - ./encryption_key.bin:/app/encryption_key.bin:ro
    
    # Environment variables (optional)
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONIOENCODING=utf-8
    
    # Logging
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
    # Health check
    healthcheck:
      test: ["CMD", "python3", "-c", "import requests; requests.get('https://api.telegram.org', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Watchtower để auto-update
  watchtower:
    image: containrrr/watchtower
    container_name: watchtower
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - WATCHTOWER_CLEANUP=true
      - WATCHTOWER_POLL_INTERVAL=3600  # Check mỗi giờ
    profiles:
      - watchtower  # Chỉ chạy khi specify profile
