#!/bin/bash

# Script cài đặt và khởi động Telegram Bot trên Linux
# Tác giả: Telegram Bot License Manager

set -e  # Dừng script nếu có lỗi

echo "=========================================="
echo "🐧 TELEGRAM BOT LINUX INSTALLER"
echo "=========================================="
echo

# Kiểm tra quyền root cho một số lệnh
check_sudo() {
    if ! sudo -n true 2>/dev/null; then
        echo "⚠️  Một số tính năng cần quyền sudo (để đọc hardware info)"
        echo "   Bạn có thể nhập password sudo khi được yêu cầu"
        echo
    fi
}

# Kiểm tra Python 3
check_python() {
    echo "🔍 Kiểm tra Python..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
        echo "   ✅ Python3 đã cài đặt: $PYTHON_VERSION"
    else
        echo "   ❌ Python3 chưa được cài đặt!"
        echo "   💡 Cài đặt Python3:"
        echo "      Ubuntu/Debian: sudo apt update && sudo apt install python3 python3-pip"
        echo "      CentOS/RHEL:   sudo yum install python3 python3-pip"
        echo "      Arch Linux:    sudo pacman -S python python-pip"
        exit 1
    fi
}

# Kiểm tra pip
check_pip() {
    echo "🔍 Kiểm tra pip..."
    
    if command -v pip3 &> /dev/null; then
        echo "   ✅ pip3 đã cài đặt"
    elif command -v pip &> /dev/null; then
        echo "   ✅ pip đã cài đặt"
    else
        echo "   ❌ pip chưa được cài đặt!"
        echo "   💡 Cài đặt pip:"
        echo "      Ubuntu/Debian: sudo apt install python3-pip"
        echo "      CentOS/RHEL:   sudo yum install python3-pip"
        exit 1
    fi
}

# Cài đặt dependencies
install_dependencies() {
    echo "📦 Cài đặt dependencies..."
    
    # Tạo virtual environment
    if [ ! -d "venv" ]; then
        echo "   🔧 Tạo virtual environment..."
        python3 -m venv venv
    fi
    
    # Kích hoạt virtual environment
    source venv/bin/activate
    
    # Upgrade pip
    echo "   ⬆️  Upgrade pip..."
    pip install --upgrade pip
    
    # Cài đặt requirements
    echo "   📥 Cài đặt packages từ requirements.txt..."
    pip install -r requirements.txt
    
    echo "   ✅ Dependencies đã được cài đặt"
}

# Kiểm tra cấu hình
check_config() {
    echo "⚙️  Kiểm tra cấu hình..."
    
    if [ ! -f "config.py" ]; then
        echo "   ❌ Không tìm thấy config.py!"
        echo "   💡 Tạo file config.py với thông tin Telegram và GitHub"
        exit 1
    fi
    
    # Kích hoạt venv và test config
    source venv/bin/activate
    python3 -c "
import sys
try:
    from config import TELEGRAM_TOKEN, TELEGRAM_ADMIN_CHAT_ID, GITHUB_TOKEN, GITHUB_REPO
    if not TELEGRAM_TOKEN or TELEGRAM_TOKEN == 'your_bot_token_here':
        print('   ❌ TELEGRAM_TOKEN chưa được cấu hình')
        sys.exit(1)
    if not GITHUB_TOKEN:
        print('   ❌ GITHUB_TOKEN chưa được cấu hình')
        sys.exit(1)
    print('   ✅ Cấu hình cơ bản OK')
except ImportError as e:
    print(f'   ❌ Lỗi import config: {e}')
    sys.exit(1)
except Exception as e:
    print(f'   ❌ Lỗi config: {e}')
    sys.exit(1)
"
}

# Tạo systemd service (optional)
create_systemd_service() {
    echo
    read -p "🔧 Bạn có muốn tạo systemd service để auto-start bot? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        SERVICE_FILE="/etc/systemd/system/telegram-bot.service"
        CURRENT_DIR=$(pwd)
        CURRENT_USER=$(whoami)
        
        echo "   📝 Tạo systemd service..."
        
        sudo tee $SERVICE_FILE > /dev/null <<EOF
[Unit]
Description=Telegram Bot License Manager
After=network.target

[Service]
Type=simple
User=$CURRENT_USER
WorkingDirectory=$CURRENT_DIR
Environment=PATH=$CURRENT_DIR/venv/bin
ExecStart=$CURRENT_DIR/venv/bin/python $CURRENT_DIR/run_telegram_bot.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
        
        sudo systemctl daemon-reload
        sudo systemctl enable telegram-bot.service
        
        echo "   ✅ Systemd service đã được tạo"
        echo "   💡 Sử dụng các lệnh sau:"
        echo "      sudo systemctl start telegram-bot    # Khởi động"
        echo "      sudo systemctl stop telegram-bot     # Dừng"
        echo "      sudo systemctl status telegram-bot   # Kiểm tra trạng thái"
        echo "      sudo journalctl -u telegram-bot -f   # Xem logs"
    fi
}

# Menu chính
show_menu() {
    echo
    echo "🚀 Chọn cách chạy bot:"
    echo "1. Chạy bot thông thường (foreground)"
    echo "2. Chạy bot với watchdog"
    echo "3. Test cấu hình"
    echo "4. Thoát"
    echo
}

# Chạy bot
run_bot() {
    source venv/bin/activate
    
    case $1 in
        1)
            echo "🚀 Khởi động bot thông thường..."
            python3 run_telegram_bot.py
            ;;
        2)
            echo "🛡️ Khởi động bot với watchdog..."
            python3 bot_watchdog.py
            ;;
        3)
            echo "🔧 Test cấu hình..."
            python3 start_bot.py
            ;;
    esac
}

# Main function
main() {
    check_sudo
    check_python
    check_pip
    install_dependencies
    check_config
    create_systemd_service
    
    while true; do
        show_menu
        read -p "Nhập lựa chọn (1-4): " choice
        
        case $choice in
            1|2|3)
                run_bot $choice
                ;;
            4)
                echo "👋 Tạm biệt!"
                exit 0
                ;;
            *)
                echo "❌ Lựa chọn không hợp lệ!"
                ;;
        esac
        
        echo
        read -p "Nhấn Enter để quay lại menu..."
    done
}

# Chạy script
main "$@"
