#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import time
import logging
from datetime import datetime

# Cấu hình logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('telegram_bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Kiểm tra và cài đặt thư viện cần thiết
try:
    import telebot
    import requests
    from cryptography.fernet import Fernet
except ImportError:
    print("Đang cài đặt các thư viện cần thiết...")
    import pip
    pip.main(['install', '-r', 'requirements.txt'])
    
    # Thử import lại
    import telebot
    import requests
    from cryptography.fernet import Fernet

def check_internet_connection():
    """Kiểm tra kết nối internet"""
    try:
        response = requests.get('https://api.telegram.org', timeout=10)
        return True
    except Exception:
        return False

def run_bot_with_retry():
    """Chạy bot với cơ chế retry tự động"""
    max_retries = 5
    retry_count = 0
    initial_delay = 5
    
    while retry_count < max_retries:
        try:
            # Import bot từ telegram_manager
            from telegram_manager import bot
            
            logger.info("Kiểm tra kết nối trước khi khởi động bot...")
            
            # Kiểm tra kết nối internet
            if not check_internet_connection():
                logger.error("Không có kết nối internet!")
                time.sleep(initial_delay * (2 ** retry_count))
                retry_count += 1
                continue
            
            logger.info("Đang khởi động bot Telegram...")
            logger.info("Nhấn Ctrl+C để dừng bot")
            
            # Reset retry count khi khởi động thành công
            retry_count = 0
            
            # Chạy bot với cấu hình timeout tốt hơn
            bot.polling(
                none_stop=True,
                interval=1,
                timeout=30,
                long_polling_timeout=30
            )
            
        except KeyboardInterrupt:
            logger.info("Bot đã được dừng bởi người dùng")
            break
            
        except requests.exceptions.Timeout:
            retry_count += 1
            delay = initial_delay * (2 ** min(retry_count - 1, 4))  # Max delay 80s
            logger.error(f"Lỗi timeout kết nối (lần thử {retry_count}/{max_retries}): Read timeout")
            logger.info(f"Đang thử kết nối lại sau {delay} giây...")
            time.sleep(delay)
            continue
            
        except requests.exceptions.ConnectionError:
            retry_count += 1
            delay = initial_delay * (2 ** min(retry_count - 1, 4))
            logger.error(f"Lỗi kết nối mạng (lần thử {retry_count}/{max_retries}): Connection error")
            logger.info(f"Đang thử kết nối lại sau {delay} giây...")
            time.sleep(delay)
            continue
            
        except telebot.apihelper.ApiException as e:
            if "timeout" in str(e).lower():
                retry_count += 1
                delay = initial_delay * (2 ** min(retry_count - 1, 4))
                logger.error(f"Lỗi Telegram API timeout (lần thử {retry_count}/{max_retries}): {str(e)}")
                logger.info(f"Đang thử kết nối lại sau {delay} giây...")
                time.sleep(delay)
                continue
            else:
                logger.error(f"Lỗi Telegram API: {str(e)}")
                break
                
        except Exception as e:
            retry_count += 1
            delay = initial_delay * (2 ** min(retry_count - 1, 4))
            logger.error(f"Lỗi khi chạy bot (lần thử {retry_count}/{max_retries}): {str(e)}")
            
            if retry_count < max_retries:
                logger.info(f"Đang thử khởi động lại sau {delay} giây...")
                time.sleep(delay)
            continue
    
    if retry_count >= max_retries:
        logger.error("Bot không thể khởi động sau nhiều lần thử. Kiểm tra lại cấu hình.")
        input("Nhấn Enter để thoát...")
    else:
        logger.info("Bot đã dừng hoạt động.")

if __name__ == "__main__":
    run_bot_with_retry() 