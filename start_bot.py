#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script khởi động Telegram Bot với nhiều tùy chọn
"""

import os
import sys
import subprocess

def print_banner():
    """In banner hệ thống"""
    print("=" * 60)
    print("🤖 TELEGRAM BOT LICENSE MANAGER")
    print("=" * 60)
    print()

def print_menu():
    """In menu lựa chọn"""
    print("Chọn cách chạy bot:")
    print()
    print("1. 🚀 Chạy bot thông thường")
    print("   - Bot sẽ chạy với auto-retry khi bị disconnect")
    print("   - Tự động reconnect khi gặp network issues")
    print()
    print("2. 🛡️ Chạy bot với Watchdog (Khuyến nghị)")
    print("   - Bot sẽ được giám sát 24/7")  
    print("   - Tự động restart khi bot crash")
    print("   - Monitor system resources")
    print()
    print("3. 🔧 Test kết nối & cấu hình")
    print("   - Ki<PERSON><PERSON> tra Telegram token")
    print("   - Kiểm tra GitHub connection")
    print("   - Validate các settings")
    print()
    print("4. 📊 Xem logs")
    print("   - Xem log bot hoạt động")
    print("   - Xem log watchdog")
    print()
    print("0. ❌ Thoát")
    print()

def run_normal_bot():
    """Chạy bot thông thường"""
    print("🚀 Đang khởi động bot thông thường...")
    print("Bot sẽ tự động retry khi gặp lỗi network.")
    print("Nhấn Ctrl+C để dừng bot.")
    print("-" * 40)
    
    try:
        result = subprocess.run([sys.executable, "run_telegram_bot.py"])
        return result.returncode
    except KeyboardInterrupt:
        print("\n✅ Bot đã được dừng bởi người dùng")
        return 0

def run_watchdog_bot():
    """Chạy bot với watchdog"""
    print("🛡️ Đang khởi động bot với Watchdog...")
    print("Watchdog sẽ giám sát và tự động restart bot khi cần.")
    print("Nhấn Ctrl+C để dừng cả bot và watchdog.")
    print("-" * 40)
    
    try:
        result = subprocess.run([sys.executable, "bot_watchdog.py"])
        return result.returncode
    except KeyboardInterrupt:
        print("\n✅ Watchdog đã được dừng bởi người dùng")
        return 0

def test_configuration():
    """Test cấu hình hệ thống"""
    print("🔧 Đang kiểm tra cấu hình...")
    print("-" * 40)
    
    # Test import các modules
    try:
        print("📦 Kiểm tra dependencies...")
        import telebot
        import requests
        import cryptography
        import psutil
        print("   ✅ Tất cả dependencies đã được cài đặt")
    except ImportError as e:
        print(f"   ❌ Thiếu dependency: {e}")
        print("   💡 Chạy: pip install -r requirements.txt")
        return False
    
    # Test config
    try:
        print("\n⚙️ Kiểm tra cấu hình...")
        from config import (
            TELEGRAM_TOKEN, TELEGRAM_ADMIN_CHAT_ID,
            GITHUB_TOKEN, GITHUB_REPO
        )
        
        if not TELEGRAM_TOKEN or TELEGRAM_TOKEN == "your_bot_token_here":
            print("   ❌ TELEGRAM_TOKEN chưa được cấu hình")
            return False
        else:
            print("   ✅ TELEGRAM_TOKEN đã được cấu hình")
        
        if not TELEGRAM_ADMIN_CHAT_ID:
            print("   ❌ TELEGRAM_ADMIN_CHAT_ID chưa được cấu hình")
            return False
        else:
            print("   ✅ TELEGRAM_ADMIN_CHAT_ID đã được cấu hình")
            
        if not GITHUB_TOKEN or not GITHUB_REPO:
            print("   ❌ GitHub config chưa được cấu hình đầy đủ")
            return False
        else:
            print("   ✅ GitHub config đã được cấu hình")
            
    except ImportError:
        print("   ❌ Không thể import config.py")
        return False
    
    # Test Telegram connection
    try:
        print("\n📱 Kiểm tra kết nối Telegram...")
        import requests
        url = f"https://api.telegram.org/bot{TELEGRAM_TOKEN}/getMe"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            bot_info = response.json()
            if bot_info.get('ok'):
                bot_name = bot_info['result']['username']
                print(f"   ✅ Kết nối Telegram thành công - Bot: @{bot_name}")
            else:
                print("   ❌ Telegram API trả về lỗi")
                return False
        else:
            print(f"   ❌ Lỗi kết nối Telegram: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Lỗi kiểm tra Telegram: {str(e)}")
        return False
    
    # Test GitHub connection
    try:
        print("\n🐙 Kiểm tra kết nối GitHub...")
        headers = {"Authorization": f"token {GITHUB_TOKEN}"}
        url = f"https://api.github.com/repos/{GITHUB_REPO}"
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            repo_info = response.json()
            print(f"   ✅ Kết nối GitHub thành công - Repo: {repo_info['full_name']}")
        else:
            print(f"   ❌ Lỗi kết nối GitHub: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Lỗi kiểm tra GitHub: {str(e)}")
        return False
    
    print("\n🎉 Tất cả cấu hình đều OK! Bot sẵn sàng hoạt động.")
    return True

def view_logs():
    """Xem logs"""
    print("📊 Logs có sẵn:")
    print()
    
    logs = [
        ("telegram_bot.log", "Log hoạt động bot"),
        ("bot_watchdog.log", "Log watchdog giám sát"),
    ]
    
    available_logs = []
    for log_file, description in logs:
        if os.path.exists(log_file):
            size = os.path.getsize(log_file)
            available_logs.append((log_file, description, size))
            print(f"{len(available_logs)}. {description} ({log_file}) - {size} bytes")
    
    if not available_logs:
        print("❌ Không có log file nào được tìm thấy")
        return
    
    print(f"{len(available_logs) + 1}. Quay lại menu chính")
    print()
    
    try:
        choice = int(input("Chọn log để xem (Enter = quay lại): ") or str(len(available_logs) + 1))
        
        if 1 <= choice <= len(available_logs):
            log_file = available_logs[choice - 1][0]
            print(f"\n📄 Nội dung {log_file} (20 dòng cuối):")
            print("-" * 60)
            
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    for line in lines[-20:]:
                        print(line.rstrip())
            except Exception as e:
                print(f"Lỗi đọc file: {e}")
                
            print("-" * 60)
            input("Nhấn Enter để tiếp tục...")
            
    except ValueError:
        pass

def main():
    """Hàm main"""
    while True:
        print_banner()
        print_menu()
        
        try:
            choice = input("Nhập lựa chọn của bạn (1-4, 0=thoát): ").strip()
            print()
            
            if choice == "0":
                print("👋 Tạm biệt!")
                break
                
            elif choice == "1":
                run_normal_bot()
                
            elif choice == "2":
                run_watchdog_bot()
                
            elif choice == "3":
                test_configuration()
                
            elif choice == "4":
                view_logs()
                
            else:
                print("❌ Lựa chọn không hợp lệ!")
                
        except KeyboardInterrupt:
            print("\n👋 Tạm biệt!")
            break
        except Exception as e:
            print(f"❌ Lỗi: {e}")
        
        if choice in ["1", "2", "3", "4"]:
            input("\nNhấn Enter để quay lại menu...")

if __name__ == "__main__":
    main() 