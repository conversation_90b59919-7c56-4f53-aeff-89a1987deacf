2025-07-28 15:30:28,078 - __main__ - INFO - <PERSON><PERSON><PERSON> tra kết nối trước khi khởi động bot...
2025-07-28 15:30:30,781 - telegram_manager - INFO - <PERSON><PERSON> khởi động bot...
2025-07-28 15:30:30,781 - telegram_manager - INFO - Admin users: 1
2025-07-28 15:30:30,781 - telegram_manager - INFO - License creators: 0
2025-07-28 15:30:31,530 - __main__ - INFO - <PERSON>ang khởi động bot Telegram...
2025-07-28 15:30:31,530 - __main__ - INFO - Nhấn Ctrl+C để dừng bot
2025-07-28 15:30:31,530 - __main__ - ERROR - Lỗi khi chạy bot (lần thử 1/5): TeleBot.polling() got an unexpected keyword argument 'retry_on_failure'
2025-07-28 15:30:31,530 - __main__ - ERROR - <PERSON><PERSON> không thể khởi động. <PERSON><PERSON><PERSON> tra lại cấu hình.
2025-07-28 15:32:52,420 - __main__ - INFO - <PERSON><PERSON><PERSON> tra kết nối trước khi khởi động bot...
2025-07-28 15:32:53,932 - __main__ - ERROR - Lỗi khi chạy bot (lần thử 1/5): cannot import name 'start_bot' from 'telegram_manager' (F:\OneDrive - MSFT\TikTok\Elevenlabs Tool\Virbo_v2\Verify\telegram_manager.py)
2025-07-28 15:32:53,932 - __main__ - ERROR - Bot không thể khởi động. Kiểm tra lại cấu hình.
2025-07-28 17:04:39,611 - __main__ - INFO - Kiểm tra kết nối trước khi khởi động bot...
2025-07-28 17:04:41,130 - __main__ - INFO - Đang khởi động bot Telegram...
2025-07-28 17:04:41,130 - __main__ - INFO - Nhấn Ctrl+C để dừng bot
2025-07-28 17:05:36,961 - __main__ - INFO - Kiểm tra kết nối trước khi khởi động bot...
2025-07-28 17:05:38,455 - __main__ - INFO - Đang khởi động bot Telegram...
2025-07-28 17:05:38,455 - __main__ - INFO - Nhấn Ctrl+C để dừng bot
2025-07-28 20:02:40,523 - __main__ - INFO - Kiểm tra kết nối trước khi khởi động bot...
2025-07-28 20:02:42,468 - __main__ - INFO - Đang khởi động bot Telegram...
2025-07-28 20:02:42,469 - __main__ - INFO - Nhấn Ctrl+C để dừng bot
2025-07-29 10:16:51,501 - __main__ - INFO - Kiểm tra kết nối trước khi khởi động bot...
2025-07-29 10:16:53,015 - __main__ - INFO - Đang khởi động bot Telegram...
2025-07-29 10:16:53,015 - __main__ - INFO - Nhấn Ctrl+C để dừng bot
2025-07-29 10:21:28,888 - __main__ - INFO - Kiểm tra kết nối trước khi khởi động bot...
2025-07-29 10:21:30,310 - __main__ - INFO - Đang khởi động bot Telegram...
2025-07-29 10:21:30,310 - __main__ - INFO - Nhấn Ctrl+C để dừng bot
2025-07-29 10:22:01,407 - __main__ - INFO - Kiểm tra kết nối trước khi khởi động bot...
2025-07-29 10:22:02,877 - __main__ - INFO - Đang khởi động bot Telegram...
2025-07-29 10:22:02,877 - __main__ - INFO - Nhấn Ctrl+C để dừng bot
2025-07-29 10:22:59,848 - __main__ - INFO - Kiểm tra kết nối trước khi khởi động bot...
2025-07-29 10:23:01,273 - __main__ - INFO - Đang khởi động bot Telegram...
2025-07-29 10:23:01,273 - __main__ - INFO - Nhấn Ctrl+C để dừng bot
2025-07-29 10:27:33,924 - __main__ - INFO - Kiểm tra kết nối trước khi khởi động bot...
2025-07-29 10:27:35,399 - __main__ - INFO - Đang khởi động bot Telegram...
2025-07-29 10:27:35,399 - __main__ - INFO - Nhấn Ctrl+C để dừng bot
2025-07-29 10:33:02,413 - __main__ - INFO - Kiểm tra kết nối trước khi khởi động bot...
2025-07-29 10:33:03,904 - __main__ - INFO - Đang khởi động bot Telegram...
2025-07-29 10:33:03,904 - __main__ - INFO - Nhấn Ctrl+C để dừng bot
2025-07-29 10:35:07,446 - __main__ - INFO - Kiểm tra kết nối trước khi khởi động bot...
2025-07-29 10:35:08,899 - __main__ - INFO - Đang khởi động bot Telegram...
2025-07-29 10:35:08,899 - __main__ - INFO - Nhấn Ctrl+C để dừng bot
2025-07-29 10:35:22,847 - TeleBot - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: can't parse entities: Can't find end of the entity starting at byte offset 528
2025-07-29 10:35:22,849 - TeleBot - ERROR - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\telebot\__init__.py", line 1235, in __threaded_polling
    self.worker_pool.raise_exceptions()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\telebot\util.py", line 151, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\telebot\util.py", line 94, in run
    task(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\telebot\__init__.py", line 9854, in _run_middlewares_and_handler
    result = handler['function'](message)
  File "F:\OneDrive - MSFT\TikTok\Elevenlabs Tool\Virbo_v2\Verify\telegram_manager.py", line 1056, in voice_ids_command
    bot.reply_to(message, guide_text, parse_mode="Markdown")
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\telebot\__init__.py", line 6067, in reply_to
    return self.send_message(message.chat.id, text, reply_parameters=reply_parameters, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\telebot\__init__.py", line 1800, in send_message
    apihelper.send_message(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\telebot\apihelper.py", line 275, in send_message
    return _make_request(token, method_url, params=payload, method='post')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\telebot\apihelper.py", line 168, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\telebot\apihelper.py", line 195, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: can't parse entities: Can't find end of the entity starting at byte offset 528

