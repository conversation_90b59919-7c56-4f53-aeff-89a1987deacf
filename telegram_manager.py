#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import telebot
import sys
import os
import importlib

# <PERSON><PERSON>u hình UTF-8 cho Windows console
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    try:
        # Thử cấu hình console UTF-8 trên Windows 10+
        import locale
        locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
    except:
        pass
from Verify_client import (
    get_hardware_info,
    generate_fingerprint,
    verify_specific_license,
    verify_license,
    create_license,
    extend_license,
    delete_license,
    list_all_licenses,
    check_and_create_licenses_folder,
    get_license_from_github,
    update_license_on_github,
    ENCRYPTION_KEY
)

# Import cấu hình trước tiên
try:
    from config import (
        TELEGRAM_TOKEN,
        TELEGRAM_ADMIN_CHAT_ID,
        TELEGRAM_ADMIN_USERS,
        TELEGRAM_LICENSE_CREATORS,
        DEFAULT_LICENSE_MODEL,
        DEFAULT_LICENSE_DURATION,
        DEFAULT_LICENSE_MINUTES,
        GITHUB_TOKEN,
        GITHUB_REPO,
        GITHUB_BRANCH,
        CONTACT_INFO
    )
except ImportError:
    # Sử dụng giá trị mặc định nếu không import được
    TELEGRAM_TOKEN = "**********************************************"
    TELEGRAM_ADMIN_CHAT_ID = "-1002161576202"
    TELEGRAM_ADMIN_USERS = []
    TELEGRAM_LICENSE_CREATORS = []
    DEFAULT_LICENSE_MODEL = "usage_only"
    DEFAULT_LICENSE_DURATION = 30
    DEFAULT_LICENSE_MINUTES = 180
    GITHUB_TOKEN = "*********************************************************************************************"
    GITHUB_REPO = "tuha1994/xacthuc"
    GITHUB_BRANCH = "main"
    CONTACT_INFO = "Hán Anh Tư - ********** (Zalo)"

# Cập nhật cấu hình cho Verify_client
import Verify_client
# Tiêm cấu hình từ config.py vào Verify_client
Verify_client.GITHUB_TOKEN = GITHUB_TOKEN
Verify_client.GITHUB_REPO = GITHUB_REPO
Verify_client.GITHUB_BRANCH = GITHUB_BRANCH
Verify_client.CONTACT_INFO = CONTACT_INFO

# Sau đó import các hàm cần thiết
from Verify_client import (
    get_hardware_info, 
    generate_fingerprint, 
    verify_specific_license, 
    verify_license,
    create_license, 
    extend_license, 
    delete_license, 
    list_all_licenses,
    check_and_create_licenses_folder,
    ENCRYPTION_KEY
)

# Token Telegram Bot
TOKEN = TELEGRAM_TOKEN
ADMIN_CHAT_ID = TELEGRAM_ADMIN_CHAT_ID
AUTHORIZED_USERS = TELEGRAM_ADMIN_USERS.copy()  # Tạo bản sao để không thay đổi cấu hình gốc
LICENSE_CREATORS = TELEGRAM_LICENSE_CREATORS.copy()  # Danh sách user chỉ được tạo license

# Khởi tạo bot
bot = telebot.TeleBot(TOKEN)

# Hàm gửi thông báo vào kênh admin với retry mechanism
def send_noti(message, max_retries=3):
    chat_id = ADMIN_CHAT_ID
    url = f"https://api.telegram.org/bot{TOKEN}/sendMessage"
    payload = {
        'chat_id': chat_id,
        'text': message
    }
    
    for attempt in range(max_retries):
        try:
            import requests
            import time
            
            response = requests.post(url, data=payload, timeout=10, headers={'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8'})
            
            if response.status_code == 200:
                return True
            elif response.status_code == 429:  # Rate limit
                # Telegram rate limit, đợi và thử lại
                retry_after = int(response.headers.get('Retry-After', 1))
                print(f"Rate limited, đợi {retry_after} giây...")
                time.sleep(retry_after)
                continue
            else:
                print(f"Lỗi gửi thông báo (attempt {attempt + 1}): Status {response.status_code}")
                
        except requests.exceptions.Timeout:
            print(f"Timeout khi gửi thông báo (attempt {attempt + 1})")
        except requests.exceptions.ConnectionError:
            print(f"Lỗi kết nối khi gửi thông báo (attempt {attempt + 1})")
        except Exception as e:
            print(f"Lỗi khi gửi thông báo (attempt {attempt + 1}): {str(e)}")
        
        # Đợi trước khi retry (exponential backoff)
        if attempt < max_retries - 1:
            wait_time = (2 ** attempt) * 1  # 1s, 2s, 4s
            print(f"Đang thử lại sau {wait_time} giây...")
            time.sleep(wait_time)
    
    print(f"Không thể gửi thông báo sau {max_retries} lần thử")
    return False

# Kiểm tra quyền admin đầy đủ
def is_authorized(user_id):
    # Chuyển user_id thành string nếu là số
    user_id_str = str(user_id)
    return user_id_str in AUTHORIZED_USERS or user_id_str == ADMIN_CHAT_ID or user_id == ADMIN_CHAT_ID

# Kiểm tra quyền tạo license (bao gồm cả admin và license creators)
def can_create_license(user_id):
    # Chuyển user_id thành string nếu là số
    user_id_str = str(user_id)
    return (user_id_str in AUTHORIZED_USERS or
            user_id_str == ADMIN_CHAT_ID or
            user_id == ADMIN_CHAT_ID or
            user_id_str in LICENSE_CREATORS)

# Kiểm tra quyền kiểm tra license (bao gồm cả admin và license creators)
def can_check_license(user_id):
    # Chuyển user_id thành string nếu là số
    user_id_str = str(user_id)
    return (user_id_str in AUTHORIZED_USERS or
            user_id_str == ADMIN_CHAT_ID or
            user_id == ADMIN_CHAT_ID or
            user_id_str in LICENSE_CREATORS)

# Command /start - Giới thiệu bot
@bot.message_handler(commands=['start'])
def start_command(message):
    user_id = message.from_user.id

    # Kiểm tra quyền của user
    is_admin = is_authorized(user_id)
    is_creator = can_create_license(user_id)

    response = "Xin chào! Đây là Bot quản lý license.\n\n"

    # Lệnh cho tất cả user
    response += "🔹 Lệnh cho người dùng:\n"
    response += "- /getfingerprint - Lấy mã máy của bạn\n"
    response += "- /check_my_license - Kiểm tra license máy hiện tại\n"
    response += "- /whoami - Xem thông tin user ID của bạn\n\n"

    # Lệnh cho license creators
    if is_creator:
        response += "🔸 Lệnh cho License Creator:\n"
        response += "- /createlic [fingerprint] [user_name] [days] [minutes] [license_model] [voice_ids] - Tạo license mới\n"
        response += "- /checklic [fingerprint] - Kiểm tra license\n"
        response += "- /debug_create [fingerprint] [user_name] [days] [minutes] [license_model] [voice_ids] - Tạo license với debug chi tiết\n"
        response += "💡 Lưu ý: Mọi hoạt động tạo license sẽ được thông báo tự động cho admin\n\n"

    # Lệnh cho admin
    if is_admin:
        response += "🔴 Lệnh cho Admin:\n"
        response += "- /listlic - Liệt kê tất cả license\n"
        response += "- /extendlic [fingerprint] [days] [minutes] - Gia hạn license\n"
        response += "- /dellic [fingerprint] - Xóa license\n"
        response += "- /add_admin [user_id] - Thêm người quản trị mới\n"
        response += "- /add_license_creator [user_id] - Thêm license creator\n"
        response += "- /remove_license_creator [user_id] - Xóa license creator\n"
        response += "- /list_license_creators - Liệt kê license creators\n"
        response += "- /check_github - Kiểm tra kết nối đến GitHub\n"
        response += "- /check_config - Kiểm tra thông tin cấu hình GitHub\n"
        response += "- /reload - Khởi động lại và áp dụng cấu hình mới\n"
        response += "- /test_github - Kiểm tra quyền truy cập GitHub (tạo file test)\n\n"

        response += "🎵 Quản lý Voice ID (Admin):\n"
        response += "- /voice_ids - Hướng dẫn quản lý Voice ID\n"
        response += "- /show_voice_ids [fingerprint] - Hiển thị Voice IDs\n"
        response += "- /add_voice_id [fingerprint] [voice_ids] - Thêm Voice ID\n"
        response += "- /remove_voice_id [fingerprint] [voice_ids] - Xóa Voice ID\n"
        response += "- /reset_voice_ids [fingerprint] [voice_ids] - Đặt lại toàn bộ Voice ID\n\n"

    response += "Cảm ơn bạn đã sử dụng bot!"

    bot.reply_to(message, response)

# Command /getfingerprint - Lấy mã máy
@bot.message_handler(commands=['getfingerprint'])
def get_fingerprint_command(message):
    try:
        hw_info = get_hardware_info()
        fingerprint = generate_fingerprint(hw_info)
        
        response = f"Mã máy của bạn là:\n\n{fingerprint}\n\nHãy gửi mã này cho admin để được cấp license."
        
        # Gửi thông báo cho admin
        user_info = f"Username: @{message.from_user.username}\nName: {message.from_user.first_name} {message.from_user.last_name}\nID: {message.from_user.id}"
        admin_notification = f"Người dùng đã yêu cầu mã máy:\n{user_info}\nFingerprint: {fingerprint}"
        send_noti(admin_notification)
        
        bot.reply_to(message, response)
    except Exception as e:
        bot.reply_to(message, f"Lỗi khi lấy mã máy: {str(e)}")

# Command /createlic - Tạo license mới (admin và license creators)
@bot.message_handler(commands=['createlic'])
def create_license_command(message):
    user_id = message.from_user.id
    if not can_create_license(user_id):
        bot.reply_to(message, "Bạn không có quyền sử dụng lệnh này!")
        return
    
    try:
        # Format: /createlic [fingerprint] [user_name] [days] [minutes] [license_model] [voice_ids]
        args = message.text.split(maxsplit=6)
        
        if len(args) < 3:
            bot.reply_to(message, "Sử dụng: /createlic [fingerprint] [user_name] [days] [minutes] [license_model] [voice_ids]\n\nVí dụ: /createlic abc123 NguoiDung 30 180 usage_only VOICE01,VOICE02")
            return
        
        fingerprint = args[1].strip()
        user_name = args[2].strip()
        
        # Sử dụng giá trị mặc định nếu không có
        days = DEFAULT_LICENSE_DURATION
        minutes = DEFAULT_LICENSE_MINUTES
        license_model = DEFAULT_LICENSE_MODEL
        voice_ids = []
        
        # Đọc các tham số còn lại nếu có
        if len(args) > 3:
            days = int(args[3])
        
        if len(args) > 4:
            minutes = int(args[4])
        
        if len(args) > 5:
            license_model = args[5].strip()
        
        if len(args) > 6:
            voice_ids_input = args[6].strip()
            if voice_ids_input:
                voice_ids = [vid.strip() for vid in voice_ids_input.split(",")]
        
        if len(fingerprint) != 64:
            bot.reply_to(message, "Fingerprint không hợp lệ! Phải có độ dài 64 ký tự.")
            return
        
        try:
            success, msg = create_license(fingerprint, user_name, days, minutes, license_model, voice_ids)
        except UnicodeEncodeError as e:
            success = False
            msg = f"Lỗi encoding: {str(e)}. Vui lòng sử dụng tên không có ký tự đặc biệt."
        except Exception as e:
            success = False
            msg = f"Lỗi không xác định: {str(e)}"

        if success:
            response = f"✅ Tạo license thành công!\n\nFingerprint: {fingerprint}\nNgười dùng: {user_name}\nThời hạn: {days} ngày\nThời lượng: {minutes} phút\nLoại license: {license_model}"
            if voice_ids:
                response += f"\nVoice IDs: {', '.join(voice_ids)}"

            # Gửi thông báo cho admin nếu người tạo là License Creator (không phải Admin)
            creator_user_id = str(message.from_user.id)
            if creator_user_id in LICENSE_CREATORS and creator_user_id not in AUTHORIZED_USERS:
                creator_info = f"👤 Người tạo: @{message.from_user.username or 'N/A'}\n📛 Tên: {message.from_user.first_name or ''} {message.from_user.last_name or ''}\n🆔 ID: {message.from_user.id}"
                admin_notification = f"🔸 LICENSE CREATOR ĐÃ TẠO LICENSE MỚI\n\n{creator_info}\n\n📄 Thông tin license:\n👤 User: {user_name}\n🔑 Fingerprint: {fingerprint[:8]}...{fingerprint[-8:]}\n📅 Thời hạn: {days} ngày\n⏱️ Thời lượng: {minutes} phút\n📋 Loại: {license_model}"
                if voice_ids:
                    admin_notification += f"\n🎵 Voice IDs: {', '.join(voice_ids)}"
                send_noti(admin_notification)
        else:
            response = f"❌ Tạo license thất bại: {msg}"

        bot.reply_to(message, response)
        
    except Exception as e:
        bot.reply_to(message, f"Lỗi khi tạo license: {str(e)}")

# Command /checklic - Kiểm tra license (admin và license creators)
@bot.message_handler(commands=['checklic'])
def check_license_command(message):
    user_id = message.from_user.id
    if not can_check_license(user_id):
        bot.reply_to(message, "Bạn không có quyền sử dụng lệnh này!")
        return
    
    try:
        args = message.text.split()
        if len(args) < 2:
            bot.reply_to(message, "Sử dụng: /checklic [fingerprint]")
            return
        
        fingerprint = args[1].strip()
        
        if len(fingerprint) != 64:
            bot.reply_to(message, "Fingerprint không hợp lệ! Phải có độ dài 64 ký tự.")
            return
        
        # Chuyển hướng đầu ra chuẩn để có thể bắt thông tin từ hàm verify_specific_license
        import io
        from contextlib import redirect_stdout
        
        f = io.StringIO()
        with redirect_stdout(f):
            result = verify_specific_license(fingerprint)
        
        output = f.getvalue()
        
        if result:
            response = f"✅ License hợp lệ!\n\n{output}"
        else:
            response = f"❌ License không hợp lệ hoặc không tồn tại:\n\n{output}"
        
        bot.reply_to(message, response)
        
    except Exception as e:
        bot.reply_to(message, f"Lỗi khi kiểm tra license: {str(e)}")

# Command /listlic - Liệt kê tất cả license (chỉ admin)
@bot.message_handler(commands=['listlic'])
def list_licenses_command(message):
    user_id = message.from_user.id
    if not is_authorized(user_id):
        bot.reply_to(message, "Bạn không có quyền sử dụng lệnh này!")
        return
    
    try:
        # Chuyển hướng đầu ra chuẩn để có thể bắt thông tin từ hàm list_all_licenses
        import io
        from contextlib import redirect_stdout
        
        f = io.StringIO()
        with redirect_stdout(f):
            list_all_licenses()
        
        output = f.getvalue()
        
        # Chia thành nhiều message nếu quá dài
        if len(output) > 4000:
            chunks = [output[i:i+4000] for i in range(0, len(output), 4000)]
            for chunk in chunks:
                bot.send_message(message.chat.id, chunk)
        else:
            bot.reply_to(message, output)
        
    except Exception as e:
        bot.reply_to(message, f"Lỗi khi liệt kê licenses: {str(e)}")

# Command /extendlic - Gia hạn license (chỉ admin)
@bot.message_handler(commands=['extendlic'])
def extend_license_command(message):
    user_id = message.from_user.id
    if not is_authorized(user_id):
        bot.reply_to(message, "Bạn không có quyền sử dụng lệnh này!")
        return
    
    try:
        # Format: /extendlic [fingerprint] [days] [minutes]
        args = message.text.split()
        
        if len(args) < 2:
            bot.reply_to(message, "Sử dụng: /extendlic [fingerprint] [days] [minutes]")
            return
        
        fingerprint = args[1].strip()
        
        # Giá trị mặc định nếu không có
        days = 0
        minutes = 0
        
        if len(args) > 2:
            days = int(args[2])
        
        if len(args) > 3:
            minutes = int(args[3])
        
        if len(fingerprint) != 64:
            bot.reply_to(message, "Fingerprint không hợp lệ! Phải có độ dài 64 ký tự.")
            return
        
        if days == 0 and minutes == 0:
            bot.reply_to(message, "Phải gia hạn ít nhất 1 ngày hoặc 1 phút!")
            return
        
        success, msg = extend_license(fingerprint, days, minutes)
        
        if success:
            # Lấy thông tin license sau khi gia hạn
            import io
            from contextlib import redirect_stdout
            
            f = io.StringIO()
            with redirect_stdout(f):
                verify_specific_license(fingerprint)
            
            output = f.getvalue()
            
            response = f"✅ Gia hạn license thành công!\n\nĐã thêm: {days} ngày, {minutes} phút\n\nThông tin license hiện tại:\n{output}"
        else:
            response = f"❌ Gia hạn license thất bại: {msg}"
        
        bot.reply_to(message, response)
        
    except Exception as e:
        bot.reply_to(message, f"Lỗi khi gia hạn license: {str(e)}")

# Command /dellic - Xóa license (chỉ admin)
@bot.message_handler(commands=['dellic'])
def delete_license_command(message):
    user_id = message.from_user.id
    if not is_authorized(user_id):
        bot.reply_to(message, "Bạn không có quyền sử dụng lệnh này!")
        return
    
    try:
        args = message.text.split()
        if len(args) < 2:
            bot.reply_to(message, "Sử dụng: /dellic [fingerprint]")
            return
        
        fingerprint = args[1].strip()
        
        if len(fingerprint) != 64:
            bot.reply_to(message, "Fingerprint không hợp lệ! Phải có độ dài 64 ký tự.")
            return
        
        # Yêu cầu xác nhận
        bot.reply_to(message, f"Bạn có chắc chắn muốn xóa license cho fingerprint này không?\n\nFingerprint: {fingerprint}\n\nGửi /confirm_delete {fingerprint} để xác nhận.")
        
    except Exception as e:
        bot.reply_to(message, f"Lỗi khi xóa license: {str(e)}")

# Command /confirm_delete - Xác nhận xóa license
@bot.message_handler(commands=['confirm_delete'])
def confirm_delete_command(message):
    user_id = message.from_user.id
    if not is_authorized(user_id):
        bot.reply_to(message, "Bạn không có quyền sử dụng lệnh này!")
        return
    
    try:
        args = message.text.split()
        if len(args) < 2:
            bot.reply_to(message, "Sử dụng: /confirm_delete [fingerprint]")
            return
        
        fingerprint = args[1].strip()
        
        if len(fingerprint) != 64:
            bot.reply_to(message, "Fingerprint không hợp lệ! Phải có độ dài 64 ký tự.")
            return
        
        success, msg = delete_license(fingerprint)
        
        if success:
            response = f"✅ Đã xóa license cho fingerprint: {fingerprint}"
        else:
            response = f"❌ Xóa license thất bại: {msg}"
        
        bot.reply_to(message, response)
        
    except Exception as e:
        bot.reply_to(message, f"Lỗi khi xóa license: {str(e)}")

# Command /add_admin - Thêm admin mới
@bot.message_handler(commands=['add_admin'])
def add_admin_command(message):
    user_id = message.from_user.id

    # Kiểm tra xem người dùng có trong danh sách admin không
    if not is_authorized(user_id):
        bot.reply_to(message, "Bạn không có quyền sử dụng lệnh này!")
        return

    try:
        args = message.text.split()
        if len(args) < 2:
            bot.reply_to(message, "Sử dụng: /add_admin [user_id]")
            return

        new_admin_id = args[1].strip()

        if new_admin_id in AUTHORIZED_USERS:
            bot.reply_to(message, f"User ID {new_admin_id} đã là admin rồi!")
            return

        AUTHORIZED_USERS.append(new_admin_id)
        bot.reply_to(message, f"Đã thêm User ID {new_admin_id} làm admin thành công!")

    except Exception as e:
        bot.reply_to(message, f"Lỗi khi thêm admin: {str(e)}")

# Command /add_license_creator - Thêm license creator mới
@bot.message_handler(commands=['add_license_creator'])
def add_license_creator_command(message):
    user_id = message.from_user.id

    # Chỉ admin mới có thể thêm license creator
    if not is_authorized(user_id):
        bot.reply_to(message, "Bạn không có quyền sử dụng lệnh này!")
        return

    try:
        args = message.text.split()
        if len(args) < 2:
            bot.reply_to(message, "Sử dụng: /add_license_creator [user_id]")
            return

        new_creator_id = args[1].strip()

        if new_creator_id in LICENSE_CREATORS:
            bot.reply_to(message, f"User ID {new_creator_id} đã là license creator rồi!")
            return

        if new_creator_id in AUTHORIZED_USERS or new_creator_id == ADMIN_CHAT_ID:
            bot.reply_to(message, f"User ID {new_creator_id} đã là admin rồi, không cần thêm làm license creator!")
            return

        LICENSE_CREATORS.append(new_creator_id)
        bot.reply_to(message, f"✅ Đã thêm User ID {new_creator_id} làm license creator thành công!\n\nNgười này có thể:\n- Tạo license (/createlic)\n- Kiểm tra license (/checklic)\n- Tạo license với debug (/debug_create)")

    except Exception as e:
        bot.reply_to(message, f"Lỗi khi thêm license creator: {str(e)}")

# Command /remove_license_creator - Xóa license creator
@bot.message_handler(commands=['remove_license_creator'])
def remove_license_creator_command(message):
    user_id = message.from_user.id

    # Chỉ admin mới có thể xóa license creator
    if not is_authorized(user_id):
        bot.reply_to(message, "Bạn không có quyền sử dụng lệnh này!")
        return

    try:
        args = message.text.split()
        if len(args) < 2:
            bot.reply_to(message, "Sử dụng: /remove_license_creator [user_id]")
            return

        creator_id = args[1].strip()

        if creator_id not in LICENSE_CREATORS:
            bot.reply_to(message, f"User ID {creator_id} không phải là license creator!")
            return

        LICENSE_CREATORS.remove(creator_id)
        bot.reply_to(message, f"✅ Đã xóa User ID {creator_id} khỏi danh sách license creator!")

    except Exception as e:
        bot.reply_to(message, f"Lỗi khi xóa license creator: {str(e)}")

# Command /list_license_creators - Liệt kê license creators
@bot.message_handler(commands=['list_license_creators'])
def list_license_creators_command(message):
    user_id = message.from_user.id

    # Chỉ admin mới có thể xem danh sách license creators
    if not is_authorized(user_id):
        bot.reply_to(message, "Bạn không có quyền sử dụng lệnh này!")
        return

    try:
        if not LICENSE_CREATORS:
            bot.reply_to(message, "📝 Chưa có license creator nào được thêm.")
            return

        response = "📝 Danh sách License Creators:\n\n"
        for i, creator_id in enumerate(LICENSE_CREATORS, 1):
            response += f"{i}. User ID: {creator_id}\n"

        response += f"\nTổng cộng: {len(LICENSE_CREATORS)} license creator(s)"
        response += f"\n\n💡 Lưu ý: Tất cả hoạt động tạo license của License Creators sẽ được thông báo tự động cho admin."
        bot.reply_to(message, response)

    except Exception as e:
        bot.reply_to(message, f"Lỗi khi liệt kê license creators: {str(e)}")

# Command /check_my_license - Kiểm tra license của máy hiện tại
@bot.message_handler(commands=['check_my_license'])
def check_my_license_command(message):
    try:
        # Chuyển hướng đầu ra chuẩn để có thể bắt thông tin từ hàm verify_license
        import io
        from contextlib import redirect_stdout
        
        f = io.StringIO()
        with redirect_stdout(f):
            result = verify_license()
        
        output = f.getvalue()
        
        if result:
            response = f"✅ License hợp lệ!\n\n{output}"
        else:
            response = f"❌ License không hợp lệ hoặc không tồn tại:\n\n{output}"
        
        bot.reply_to(message, response)
        
    except Exception as e:
        bot.reply_to(message, f"Lỗi khi kiểm tra license: {str(e)}")

# Command /check_github - Kiểm tra kết nối đến GitHub (chỉ admin)
@bot.message_handler(commands=['check_github'])
def check_github_command(message):
    user_id = message.from_user.id
    if not is_authorized(user_id):
        bot.reply_to(message, "Bạn không có quyền sử dụng lệnh này!")
        return
    
    try:
        # Chuyển hướng đầu ra chuẩn để bắt thông tin
        import io
        from contextlib import redirect_stdout
        
        f = io.StringIO()
        with redirect_stdout(f):
            result = check_and_create_licenses_folder()
        
        output = f.getvalue()
        
        if result:
            response = f"✅ Kết nối đến GitHub thành công!\n\nThư mục licenses sẵn sàng để sử dụng.\n\nThông tin chi tiết:\n{output}"
        else:
            response = f"❌ Kết nối đến GitHub thất bại!\n\nVui lòng kiểm tra lại GitHub Token và thông tin repository.\n\nThông tin lỗi:\n{output}"
        
        bot.reply_to(message, response)
        
    except Exception as e:
        bot.reply_to(message, f"Lỗi khi kiểm tra kết nối GitHub: {str(e)}")

# Command /debug_create - Tạo license với chế độ debug (admin và license creators)
@bot.message_handler(commands=['debug_create'])
def debug_create_license_command(message):
    user_id = message.from_user.id
    if not can_create_license(user_id):
        bot.reply_to(message, "Bạn không có quyền sử dụng lệnh này!")
        return
    
    try:
        # Format: /debug_create [fingerprint] [user_name] [days] [minutes] [license_model] [voice_ids]
        args = message.text.split(maxsplit=6)
        
        if len(args) < 3:
            bot.reply_to(message, "Sử dụng: /debug_create [fingerprint] [user_name] [days] [minutes] [license_model] [voice_ids]")
            return
        
        fingerprint = args[1].strip()
        user_name = args[2].strip()
        
        # Sử dụng giá trị mặc định nếu không có
        days = DEFAULT_LICENSE_DURATION
        minutes = DEFAULT_LICENSE_MINUTES
        license_model = DEFAULT_LICENSE_MODEL
        voice_ids = []
        
        # Đọc các tham số còn lại nếu có
        if len(args) > 3:
            days = int(args[3])
        
        if len(args) > 4:
            minutes = int(args[4])
        
        if len(args) > 5:
            license_model = args[5].strip()
        
        if len(args) > 6:
            voice_ids_input = args[6].strip()
            if voice_ids_input:
                voice_ids = [vid.strip() for vid in voice_ids_input.split(",")]
        
        if len(fingerprint) != 64:
            bot.reply_to(message, "Fingerprint không hợp lệ! Phải có độ dài 64 ký tự.")
            return
        
        # Gửi thông báo đang xử lý
        processing_msg = bot.reply_to(message, "⏳ Đang xử lý... Vui lòng đợi trong giây lát.")
        
        # Chuyển hướng đầu ra chuẩn để bắt thông tin debug
        import io
        from contextlib import redirect_stdout
        
        f = io.StringIO()
        with redirect_stdout(f):
            # Kiểm tra kết nối GitHub trước
            github_check = check_and_create_licenses_folder()
            if not github_check:
                bot.edit_message_text("❌ Không thể kết nối đến GitHub! Vui lòng kiểm tra lại token và cấu hình.", 
                                     message.chat.id, processing_msg.message_id)
                return
            
            # Tiến hành tạo license
            success, msg = create_license(fingerprint, user_name, days, minutes, license_model, voice_ids)
        
        debug_output = f.getvalue()
        
        if success:
            response = f"✅ Tạo license thành công!\n\nFingerprint: {fingerprint}\nNgười dùng: {user_name}\nThời hạn: {days} ngày\nThời lượng: {minutes} phút\nLoại license: {license_model}"
            if voice_ids:
                response += f"\nVoice IDs: {', '.join(voice_ids)}"

            response += f"\n\n🔍 Chi tiết debug:\n{debug_output}"

            # Gửi thông báo cho admin nếu người tạo là License Creator (không phải Admin)
            creator_user_id = str(message.from_user.id)
            if creator_user_id in LICENSE_CREATORS and creator_user_id not in AUTHORIZED_USERS:
                creator_info = f"👤 Người tạo: @{message.from_user.username or 'N/A'}\n📛 Tên: {message.from_user.first_name or ''} {message.from_user.last_name or ''}\n🆔 ID: {message.from_user.id}"
                admin_notification = f"🔸 LICENSE CREATOR ĐÃ TẠO LICENSE (DEBUG MODE)\n\n{creator_info}\n\n📄 Thông tin license:\n👤 User: {user_name}\n🔑 Fingerprint: {fingerprint[:8]}...{fingerprint[-8:]}\n📅 Thời hạn: {days} ngày\n⏱️ Thời lượng: {minutes} phút\n📋 Loại: {license_model}"
                if voice_ids:
                    admin_notification += f"\n🎵 Voice IDs: {', '.join(voice_ids)}"
                admin_notification += f"\n\n🔍 Sử dụng debug mode"
                send_noti(admin_notification)
        else:
            response = f"❌ Tạo license thất bại: {msg}\n\n🔍 Chi tiết debug:\n{debug_output}"

        # Cập nhật thông báo với kết quả
        bot.edit_message_text(response, message.chat.id, processing_msg.message_id)
        
    except Exception as e:
        bot.reply_to(message, f"Lỗi khi tạo license: {str(e)}")

# Command /check_config - Kiểm tra thông tin cấu hình GitHub (chỉ admin)
@bot.message_handler(commands=['check_config'])
def check_config_command(message):
    user_id = message.from_user.id
    if not is_authorized(user_id):
        bot.reply_to(message, "Bạn không có quyền sử dụng lệnh này!")
        return
    
    try:
        # Ẩn một phần token để bảo mật
        def mask_token(token, visible_chars=4):
            if len(token) <= visible_chars * 2:
                return token
            return token[:visible_chars] + '*' * (len(token) - visible_chars * 2) + token[-visible_chars:]
        
        masked_github_token = mask_token(GITHUB_TOKEN)
        masked_encryption_key = mask_token(str(ENCRYPTION_KEY))
        
        # Kiểm tra độ dài token GitHub
        token_status = "✅ Hợp lệ" if len(GITHUB_TOKEN) >= 30 else "❌ Quá ngắn, có thể không hợp lệ"
        
        # Kiểm tra định dạng repo
        repo_status = "✅ Hợp lệ" if "/" in GITHUB_REPO else "❌ Không hợp lệ, phải có định dạng username/repo"
        
        # Hiển thị giá trị từ config.py và giá trị thực tế trong Verify_client
        response = f"""
📋 Thông tin cấu hình GitHub:

🔄 Cấu hình trong config.py:
🔑 GitHub Token: {masked_github_token}
   Trạng thái: {token_status}
📁 Repository: {GITHUB_REPO}
   Trạng thái: {repo_status}
🔀 Branch: {GITHUB_BRANCH}

🔄 Cấu hình trong Verify_client:
🔑 GitHub Token: {mask_token(Verify_client.GITHUB_TOKEN)}
📁 Repository: {Verify_client.GITHUB_REPO}
🔀 Branch: {Verify_client.GITHUB_BRANCH}

🔐 Encryption Key: {masked_encryption_key}

⚠️ Lưu ý: Nếu có vấn đề với GitHub Token hoặc Repository, bạn cần sửa trong file config.py
"""
        
        bot.reply_to(message, response)
        
    except Exception as e:
        bot.reply_to(message, f"Lỗi khi kiểm tra cấu hình: {str(e)}")

# Command /reload - Khởi động lại bot (chỉ admin)
@bot.message_handler(commands=['reload'])
def reload_command(message):
    user_id = message.from_user.id
    if not is_authorized(user_id):
        bot.reply_to(message, "Bạn không có quyền sử dụng lệnh này!")
        return
    
    try:
        # Gửi thông báo đang khởi động lại
        bot.reply_to(message, "🔄 Đang khởi động lại bot...")
        
        # Nạp lại module Verify_client
        importlib.reload(Verify_client)
        
        # Cập nhật lại cấu hình
        Verify_client.GITHUB_TOKEN = GITHUB_TOKEN
        Verify_client.GITHUB_REPO = GITHUB_REPO
        Verify_client.GITHUB_BRANCH = GITHUB_BRANCH
        Verify_client.CONTACT_INFO = CONTACT_INFO
        
        # Kiểm tra kết nối GitHub để đảm bảo mọi thứ hoạt động
        import io
        from contextlib import redirect_stdout
        
        f = io.StringIO()
        with redirect_stdout(f):
            result = Verify_client.check_and_create_licenses_folder()
        
        output = f.getvalue()
        
        if result:
            response = f"✅ Khởi động lại thành công!\n\nKết nối GitHub OK\nCác cấu hình đã được áp dụng."
        else:
            response = f"⚠️ Khởi động lại thành công, nhưng có vấn đề kết nối GitHub!\n\nLỗi:\n{output}"
        
        bot.reply_to(message, response)
        
    except Exception as e:
        bot.reply_to(message, f"❌ Lỗi khi khởi động lại: {str(e)}")

# Command /test_github - Kiểm tra quyền truy cập và tải file lên GitHub (chỉ admin)
@bot.message_handler(commands=['test_github'])
def test_github_command(message):
    user_id = message.from_user.id
    if not is_authorized(user_id):
        bot.reply_to(message, "Bạn không có quyền sử dụng lệnh này!")
        return
    
    try:
        import requests
        import base64
        import json
        import time
        
        # Gửi thông báo đang xử lý
        processing_msg = bot.reply_to(message, "⏳ Đang kiểm tra quyền truy cập GitHub... Vui lòng đợi trong giây lát.")
        
        # Khởi tạo chuỗi kết quả
        results = []
        
        # Kiểm tra 1: Truy cập thông tin repository
        repo_api_url = f"https://api.github.com/repos/{GITHUB_REPO}"
        headers = {
            "Authorization": f"token {GITHUB_TOKEN}",
            "Accept": "application/vnd.github.v3+json"
        }
        
        repo_response = requests.get(repo_api_url, headers=headers)
        if repo_response.status_code == 200:
            repo_data = repo_response.json()
            results.append(f"✅ 1. Truy cập repository thành công: {repo_data.get('full_name')} (Private: {repo_data.get('private')})")
        else:
            results.append(f"❌ 1. Không thể truy cập repository: {repo_response.status_code} {repo_response.text[:100]}")
            # Nếu không truy cập được repository, không cần kiểm tra tiếp
            bot.edit_message_text("\n".join(results[:4]), message.chat.id, processing_msg.message_id)
            return
        
        # Kiểm tra 2: Liệt kê nội dung repository
        contents_api_url = f"https://api.github.com/repos/{GITHUB_REPO}/contents"
        contents_response = requests.get(contents_api_url, headers=headers)
        if contents_response.status_code == 200:
            contents_data = contents_response.json()
            file_count = len(contents_data)
            results.append(f"✅ 2. Đọc nội dung repository thành công: {file_count} files/folders")
        else:
            results.append(f"❌ 2. Không thể đọc nội dung repository: {contents_response.status_code} {contents_response.text[:100]}")
        
        # Kiểm tra 3: Thư mục licenses tồn tại hoặc có thể tạo
        licenses_api_url = f"https://api.github.com/repos/{GITHUB_REPO}/contents/licenses"
        licenses_response = requests.get(licenses_api_url, headers=headers)
        
        if licenses_response.status_code == 200:
            results.append("✅ 3. Thư mục licenses đã tồn tại")
            licenses_exists = True
        else:
            results.append("⚠️ 3. Thư mục licenses chưa tồn tại, sẽ thử tạo")
            licenses_exists = False
        
        # Kiểm tra 4: Tạo file test trong thư mục licenses
        test_file_name = f"test_{int(time.time())}.txt"
        test_content = f"Test file tạo lúc {time.strftime('%Y-%m-%d %H:%M:%S')}"
        encoded_content = base64.b64encode(test_content.encode('utf-8')).decode()
        
        if licenses_exists:
            # Tạo file test trong thư mục licenses
            test_api_url = f"https://api.github.com/repos/{GITHUB_REPO}/contents/licenses/{test_file_name}"
        else:
            # Tạo file README.md để tạo thư mục licenses
            test_api_url = f"https://api.github.com/repos/{GITHUB_REPO}/contents/licenses/README.md"
            test_content = "# Licenses Folder\nThis folder stores license files."
            encoded_content = base64.b64encode(test_content.encode('utf-8')).decode()
        
        test_data = {
            "message": "Test file tạo bởi Telegram bot",
            "content": encoded_content,
            "branch": GITHUB_BRANCH
        }
        
        test_response = requests.put(test_api_url, headers=headers, json=test_data)
        
        if test_response.status_code in [200, 201]:
            if licenses_exists:
                results.append(f"✅ 4. Tạo file test thành công: {test_file_name}")
            else:
                results.append("✅ 4. Đã tạo thư mục licenses thành công")
            
            # Nếu tạo file thành công và là file test, xóa nó đi
            if licenses_exists:
                # Lấy sha của file
                test_data = test_response.json()
                file_sha = test_data.get("content", {}).get("sha")
                
                # Xóa file test
                delete_data = {
                    "message": "Xóa file test",
                    "sha": file_sha,
                    "branch": GITHUB_BRANCH
                }
                
                delete_response = requests.delete(test_api_url, headers=headers, json=delete_data)
                
                if delete_response.status_code == 200:
                    results.append(f"✅ 5. Đã xóa file test")
                else:
                    results.append(f"⚠️ 5. Không thể xóa file test: {delete_response.status_code}")
        else:
            if licenses_exists:
                results.append(f"❌ 4. Không thể tạo file test: {test_response.status_code} {test_response.text[:100]}")
            else:
                results.append(f"❌ 4. Không thể tạo thư mục licenses: {test_response.status_code} {test_response.text[:100]}")
        
        # Kết luận
        if all(r.startswith("✅") for r in results[:4]):  # Kiểm tra 4 bước đầu tiên
            results.append("\n✅ KẾT LUẬN: GitHub Token có đầy đủ quyền truy cập, có thể tạo và quản lý licenses!")
        else:
            results.append("\n❌ KẾT LUẬN: Có vấn đề với quyền truy cập GitHub, cần kiểm tra lại token!")
        
        # Gửi kết quả
        bot.edit_message_text("\n".join(results), message.chat.id, processing_msg.message_id)
        
    except Exception as e:
        bot.reply_to(message, f"❌ Lỗi khi kiểm tra GitHub: {str(e)}")

# Command /whoami - Hiển thị thông tin người dùng và trạng thái quyền
@bot.message_handler(commands=['whoami'])
def whoami_command(message):
    user_id = message.from_user.id
    chat_id = message.chat.id
    is_admin = is_authorized(user_id)
    is_creator = can_create_license(user_id)
    user_id_str = str(user_id)

    response = f"👤 Thông tin của bạn:\n"
    response += f"User ID: {user_id}\n"
    response += f"Chat ID: {chat_id}\n\n"

    response += f"🔐 Quyền hạn:\n"
    if is_admin:
        response += f"✅ Admin (toàn quyền)\n"
    elif user_id_str in LICENSE_CREATORS:
        response += f"🔸 License Creator (tạo & kiểm tra license)\n"
    else:
        response += f"👤 User thông thường\n"

    response += f"\n📊 Thống kê hệ thống:\n"
    response += f"ADMIN_CHAT_ID: {ADMIN_CHAT_ID}\n"
    response += f"Số Admin: {len(AUTHORIZED_USERS)}\n"
    response += f"Số License Creator: {len(LICENSE_CREATORS)}"

    # Chỉ admin mới thấy danh sách chi tiết
    if is_admin:
        response += f"\n\n🔍 Chi tiết (chỉ admin thấy):\n"
        response += f"Danh sách Admin: {AUTHORIZED_USERS}\n"
        response += f"Danh sách License Creator: {LICENSE_CREATORS}"

    bot.reply_to(message, response)

# Xử lý các tin nhắn không nhận diện được
@bot.message_handler(func=lambda message: True)
def echo_all(message):
    bot.reply_to(message, """
Xin chào! Tôi là Bot quản lý license.
Sử dụng /start để xem hướng dẫn sử dụng.
""")

# Command /voice_ids - Quản lý Voice IDs cho license (chỉ admin)
@bot.message_handler(commands=['voice_ids'])
def voice_ids_command(message):
    user_id = message.from_user.id
    user_id_str = str(user_id)

    # Debug thông tin quyền
    debug_info = f"🔍 Debug quyền:\n"
    debug_info += f"User ID: {user_id} (type: {type(user_id)})\n"
    debug_info += f"User ID str: {user_id_str}\n"
    debug_info += f"ADMIN_CHAT_ID: {ADMIN_CHAT_ID} (type: {type(ADMIN_CHAT_ID)})\n"
    debug_info += f"AUTHORIZED_USERS: {AUTHORIZED_USERS}\n"
    debug_info += f"is_authorized result: {is_authorized(user_id)}\n"
    debug_info += f"user_id_str == ADMIN_CHAT_ID: {user_id_str == ADMIN_CHAT_ID}\n"
    debug_info += f"user_id == ADMIN_CHAT_ID: {user_id == ADMIN_CHAT_ID}\n"
    debug_info += f"user_id_str in AUTHORIZED_USERS: {user_id_str in AUTHORIZED_USERS}\n\n"

    if not is_authorized(user_id):
        bot.reply_to(message, f"Bạn không có quyền sử dụng lệnh này!\n\n{debug_info}")
        return
    
    # Hướng dẫn sử dụng
    guide_text = """
📢 *Quản lý Voice IDs*

Sử dụng các lệnh sau để quản lý Voice IDs cho license:

1️⃣ Hiển thị Voice IDs:
`/show_voice_ids [fingerprint]`

2️⃣ Thêm Voice ID:
`/add_voice_id [fingerprint] [voice_id1,voice_id2,...]`

3️⃣ Xóa Voice ID:
`/remove_voice_id [fingerprint] [voice_id1,voice_id2,...]`

4️⃣ Đặt lại toàn bộ Voice IDs:
`/reset_voice_ids [fingerprint] [voice_id1,voice_id2,...]`
*Để bỏ giới hạn, hãy bỏ trống phần voice_ids*

⚠️ Lưu ý: 
- Tất cả voice_id phải được phân cách bằng dấu phẩy, không có khoảng trắng
- Nếu không có giới hạn Voice ID (danh sách trống), license sẽ dùng được tất cả Voice IDs
"""
    
    bot.reply_to(message, guide_text, parse_mode="Markdown")

# Command /show_voice_ids - Hiển thị Voice IDs của license (chỉ admin)
@bot.message_handler(commands=['show_voice_ids'])
def show_voice_ids_command(message):
    user_id = message.from_user.id
    if not is_authorized(user_id):
        bot.reply_to(message, "Bạn không có quyền sử dụng lệnh này!")
        return
    
    try:
        args = message.text.split()
        if len(args) < 2:
            bot.reply_to(message, "Sử dụng: /show_voice_ids [fingerprint]")
            return
        
        fingerprint = args[1].strip()
        
        if len(fingerprint) != 64:
            bot.reply_to(message, "Fingerprint không hợp lệ! Phải có độ dài 64 ký tự.")
            return
        
        # Lấy thông tin license
        success, license_data, file_sha = get_license_from_github(fingerprint)
        
        if not success:
            bot.reply_to(message, f"❌ Không tìm thấy license cho fingerprint này: {license_data}")
            return
        
        # Lấy thông tin voice_ids
        voice_ids = license_data.get("voice_ids", [])
        
        if voice_ids:
            response = f"📝 Voice IDs của license {fingerprint[:8]}...{fingerprint[-8:]}:\n\n"
            for i, vid in enumerate(voice_ids, 1):
                response += f"{i}. {vid}\n"
        else:
            response = f"📝 License {fingerprint[:8]}...{fingerprint[-8:]} không có giới hạn Voice ID (tất cả Voice IDs đều được phép sử dụng)"
        
        bot.reply_to(message, response)
        
    except Exception as e:
        bot.reply_to(message, f"❌ Lỗi khi hiển thị Voice IDs: {str(e)}")

# Command /add_voice_id - Thêm Voice ID cho license (chỉ admin)
@bot.message_handler(commands=['add_voice_id'])
def add_voice_id_command(message):
    user_id = message.from_user.id
    if not is_authorized(user_id):
        bot.reply_to(message, "Bạn không có quyền sử dụng lệnh này!")
        return
    
    try:
        args = message.text.split(maxsplit=2)
        if len(args) < 3:
            bot.reply_to(message, "Sử dụng: /add_voice_id [fingerprint] [voice_id1,voice_id2,...]")
            return
        
        fingerprint = args[1].strip()
        voice_ids_input = args[2].strip()
        
        if len(fingerprint) != 64:
            bot.reply_to(message, "Fingerprint không hợp lệ! Phải có độ dài 64 ký tự.")
            return
        
        if not voice_ids_input:
            bot.reply_to(message, "Bạn phải nhập ít nhất một Voice ID!")
            return
        
        # Lấy thông tin license
        success, license_data, file_sha = get_license_from_github(fingerprint)
        
        if not success:
            bot.reply_to(message, f"❌ Không tìm thấy license cho fingerprint này: {license_data}")
            return
        
        # Lấy danh sách voice_ids hiện tại và thêm voice_ids mới
        current_voice_ids = license_data.get("voice_ids", [])
        new_voice_ids = [vid.strip() for vid in voice_ids_input.split(",")]
        
        # Loại bỏ trùng lặp
        added_voice_ids = []
        for vid in new_voice_ids:
            if vid and vid not in current_voice_ids:
                current_voice_ids.append(vid)
                added_voice_ids.append(vid)
        
        if not added_voice_ids:
            bot.reply_to(message, "❌ Không có Voice ID mới nào được thêm vào! Tất cả các Voice ID đã tồn tại trong license.")
            return
        
        # Cập nhật license
        license_data["voice_ids"] = current_voice_ids
        
        # Gửi thông báo đang xử lý
        processing_msg = bot.reply_to(message, "⏳ Đang cập nhật Voice IDs... Vui lòng đợi trong giây lát.")
        
        # Cập nhật lên GitHub
        success, msg = update_license_on_github(fingerprint, license_data, file_sha)
        
        if success:
            response = f"✅ Đã thêm {len(added_voice_ids)} Voice ID vào license!\n\nDanh sách Voice ID đã thêm:\n"
            for i, vid in enumerate(added_voice_ids, 1):
                response += f"{i}. {vid}\n"
            
            response += f"\nTổng số Voice ID hiện tại: {len(current_voice_ids)}"
            bot.edit_message_text(response, message.chat.id, processing_msg.message_id)
        else:
            bot.edit_message_text(f"❌ Không thể cập nhật Voice IDs: {msg}", message.chat.id, processing_msg.message_id)
        
    except Exception as e:
        bot.reply_to(message, f"❌ Lỗi khi thêm Voice ID: {str(e)}")

# Command /remove_voice_id - Xóa Voice ID khỏi license (chỉ admin)
@bot.message_handler(commands=['remove_voice_id'])
def remove_voice_id_command(message):
    user_id = message.from_user.id
    if not is_authorized(user_id):
        bot.reply_to(message, "Bạn không có quyền sử dụng lệnh này!")
        return
    
    try:
        args = message.text.split(maxsplit=2)
        if len(args) < 3:
            bot.reply_to(message, "Sử dụng: /remove_voice_id [fingerprint] [voice_id1,voice_id2,...]")
            return
        
        fingerprint = args[1].strip()
        voice_ids_input = args[2].strip()
        
        if len(fingerprint) != 64:
            bot.reply_to(message, "Fingerprint không hợp lệ! Phải có độ dài 64 ký tự.")
            return
        
        if not voice_ids_input:
            bot.reply_to(message, "Bạn phải nhập ít nhất một Voice ID để xóa!")
            return
        
        # Lấy thông tin license
        success, license_data, file_sha = get_license_from_github(fingerprint)
        
        if not success:
            bot.reply_to(message, f"❌ Không tìm thấy license cho fingerprint này: {license_data}")
            return
        
        # Lấy danh sách voice_ids hiện tại
        current_voice_ids = license_data.get("voice_ids", [])
        
        if not current_voice_ids:
            bot.reply_to(message, "⚠️ License này không có giới hạn Voice ID (tất cả đều được phép)!")
            return
        
        # Voice IDs cần xóa
        remove_voice_ids = [vid.strip() for vid in voice_ids_input.split(",")]
        
        # Xóa voice_ids
        removed_voice_ids = []
        for vid in remove_voice_ids:
            if vid and vid in current_voice_ids:
                current_voice_ids.remove(vid)
                removed_voice_ids.append(vid)
        
        if not removed_voice_ids:
            bot.reply_to(message, "❌ Không có Voice ID nào được xóa! Các Voice ID không tồn tại trong license.")
            return
        
        # Cập nhật license
        license_data["voice_ids"] = current_voice_ids
        
        # Gửi thông báo đang xử lý
        processing_msg = bot.reply_to(message, "⏳ Đang cập nhật Voice IDs... Vui lòng đợi trong giây lát.")
        
        # Cập nhật lên GitHub
        success, msg = update_license_on_github(fingerprint, license_data, file_sha)
        
        if success:
            response = f"✅ Đã xóa {len(removed_voice_ids)} Voice ID khỏi license!\n\nDanh sách Voice ID đã xóa:\n"
            for i, vid in enumerate(removed_voice_ids, 1):
                response += f"{i}. {vid}\n"
            
            if current_voice_ids:
                response += f"\nTổng số Voice ID còn lại: {len(current_voice_ids)}"
            else:
                response += "\nLicense này giờ không có giới hạn Voice ID (tất cả đều được phép)!"
            
            bot.edit_message_text(response, message.chat.id, processing_msg.message_id)
        else:
            bot.edit_message_text(f"❌ Không thể cập nhật Voice IDs: {msg}", message.chat.id, processing_msg.message_id)
        
    except Exception as e:
        bot.reply_to(message, f"❌ Lỗi khi xóa Voice ID: {str(e)}")

# Command /reset_voice_ids - Đặt lại toàn bộ Voice IDs (chỉ admin)
@bot.message_handler(commands=['reset_voice_ids'])
def reset_voice_ids_command(message):
    user_id = message.from_user.id
    if not is_authorized(user_id):
        bot.reply_to(message, "Bạn không có quyền sử dụng lệnh này!")
        return
    
    try:
        args = message.text.split(maxsplit=2)
        if len(args) < 2:
            bot.reply_to(message, "Sử dụng: /reset_voice_ids [fingerprint] [voice_id1,voice_id2,...]\nĐể bỏ giới hạn, hãy bỏ trống phần voice_ids")
            return
        
        fingerprint = args[1].strip()
        
        if len(fingerprint) != 64:
            bot.reply_to(message, "Fingerprint không hợp lệ! Phải có độ dài 64 ký tự.")
            return
        
        # Lấy Voice IDs mới nếu có
        new_voice_ids = []
        if len(args) > 2:
            voice_ids_input = args[2].strip()
            if voice_ids_input:
                new_voice_ids = [vid.strip() for vid in voice_ids_input.split(",")]
        
        # Lấy thông tin license
        success, license_data, file_sha = get_license_from_github(fingerprint)
        
        if not success:
            bot.reply_to(message, f"❌ Không tìm thấy license cho fingerprint này: {license_data}")
            return
        
        # Lưu Voice IDs cũ để hiển thị
        old_voice_ids = license_data.get("voice_ids", [])
        
        # Cập nhật license
        license_data["voice_ids"] = new_voice_ids
        
        # Gửi thông báo đang xử lý
        processing_msg = bot.reply_to(message, "⏳ Đang đặt lại Voice IDs... Vui lòng đợi trong giây lát.")
        
        # Cập nhật lên GitHub
        success, msg = update_license_on_github(fingerprint, license_data, file_sha)
        
        if success:
            response = f"✅ Đã đặt lại Voice IDs cho license!\n\n"
            
            if old_voice_ids:
                response += f"Voice IDs cũ ({len(old_voice_ids)}):\n"
                for i, vid in enumerate(old_voice_ids, 1):
                    if i <= 10:  # Chỉ hiển thị tối đa 10 Voice ID cũ
                        response += f"{i}. {vid}\n"
                    elif i == 11:
                        response += f"... và {len(old_voice_ids) - 10} Voice ID khác\n"
                response += "\n"
            else:
                response += "License trước đây không có giới hạn Voice ID.\n\n"
            
            if new_voice_ids:
                response += f"Voice IDs mới ({len(new_voice_ids)}):\n"
                for i, vid in enumerate(new_voice_ids, 1):
                    response += f"{i}. {vid}\n"
            else:
                response += "License giờ không có giới hạn Voice ID (tất cả đều được phép)!"
            
            bot.edit_message_text(response, message.chat.id, processing_msg.message_id)
        else:
            bot.edit_message_text(f"❌ Không thể cập nhật Voice IDs: {msg}", message.chat.id, processing_msg.message_id)
        
    except Exception as e:
        bot.reply_to(message, f"❌ Lỗi khi đặt lại Voice IDs: {str(e)}")

if __name__ == "__main__":
    # Kiểm tra kết nối đến GitHub repository
    if not check_and_create_licenses_folder():
        print("Không thể kết nối đến GitHub repository! Vui lòng kiểm tra lại GitHub Token.")
        sys.exit(1)

    # Thêm admin mặc định
    if ADMIN_CHAT_ID and ADMIN_CHAT_ID not in AUTHORIZED_USERS:
        AUTHORIZED_USERS.append(ADMIN_CHAT_ID)

    print("Đang khởi động bot...")
    print(f"Admin users: {len(AUTHORIZED_USERS)}")
    print(f"License creators: {len(LICENSE_CREATORS)}")

    send_noti("🚀 Bot quản lý license đã khởi động!")

    # Chạy bot
    try:
        bot.polling(none_stop=True)
    except Exception as e:
        print(f"Lỗi khi chạy bot: {str(e)}")
        send_noti(f"❌ Bot gặp lỗi và đã dừng: {str(e)}")