#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# <PERSON><PERSON><PERSON> hình UTF-8 cho Windows console
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    try:
        # Thử cấu hình console UTF-8 trên Windows 10+
        import locale
        locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
    except:
        pass

# Test import
try:
    from Verify_client import create_license
    print("✓ Import thành công")
except Exception as e:
    print(f"✗ Lỗi import: {e}")
    sys.exit(1)

# Test tạo license với tên tiếng Việt
def test_create_license():
    print("Đang test tạo license với tên tiếng Việt...")
    
    fingerprint = "e1ae1435ac74cb08d2b2adbf72c3fd1da52e2c1ffc7bc2ea10d56a0a03dc5ed9"
    user_name = "Nguyễn Văn A"  # Tên có ký tự tiếng Việt
    days = 30
    minutes = 0
    license_model = "time_based"
    voice_ids = ["DANGNHI_RevengeTies", "DANGNHI_Revenge", "DAINAM_Matilda", "HOA_BRIAN", "Free"]
    
    try:
        success, message = create_license(fingerprint, user_name, days, minutes, license_model, voice_ids)
        if success:
            print(f"✓ Tạo license thành công: {message}")
        else:
            print(f"✗ Tạo license thất bại: {message}")
    except UnicodeEncodeError as e:
        print(f"✗ Lỗi encoding: {e}")
    except Exception as e:
        print(f"✗ Lỗi khác: {e}")

if __name__ == "__main__":
    test_create_license()
